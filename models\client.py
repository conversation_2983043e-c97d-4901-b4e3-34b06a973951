"""
Client data model for PACE application.
"""

class Client:
    """
    Class representing a client in the PACE application.
    """

    def __init__(self, name="", address="", insurance_carrier="", law_firm=""):
        """
        Initialize a new Client object.

        Args:
            name (str): The client's name
            address (str): The client's address
            insurance_carrier (str): The client's insurance carrier
            law_firm (str): The client's law firm
        """
        self.name = name
        self.address = address
        self.insurance_carrier = insurance_carrier
        self.law_firm = law_firm

        # Additional attributes for document generation
        self.needs_baa = False
        self.needs_dpa = False
        self.is_tasb = False  # Texas Association of School Boards (for Baker & Hostetler LLP)

    def is_valid(self):
        """
        Check if the client data is valid.

        Returns:
            bool: True if the client data is valid, False otherwise
        """
        return bool(self.name.strip())  # At minimum, client name is required

    def to_dict(self):
        """
        Convert the client data to a dictionary.

        Returns:
            dict: A dictionary containing the client data
        """
        return {
            "name": self.name,
            "address": self.address,
            "insurance_carrier": self.insurance_carrier,
            "law_firm": self.law_firm,
            "needs_baa": self.needs_baa,
            "needs_dpa": self.needs_dpa,
            "is_tasb": self.is_tasb
        }

    @classmethod
    def from_dict(cls, data):
        """
        Create a Client object from a dictionary.

        Args:
            data (dict): A dictionary containing client data

        Returns:
            Client: A new Client object
        """
        client = cls(
            name=data.get("name", ""),
            address=data.get("address", ""),
            insurance_carrier=data.get("insurance_carrier", ""),
            law_firm=data.get("law_firm", "")
        )

        # Set additional attributes
        client.needs_baa = data.get("needs_baa", False)
        client.needs_dpa = data.get("needs_dpa", False)
        client.is_tasb = data.get("is_tasb", False)

        return client
