"""
Path resolver for PACE application.
This module provides functions to resolve paths in both development and bundled environments.
"""

import os
import sys

def is_bundled():
    """
    Check if we're running in a bundled environment (PyInstaller or Nuitka).

    Returns:
        bool: True if running in a bundled environment, False otherwise
    """
    # PyInstaller detection
    pyinstaller_bundled = getattr(sys, 'frozen', False) and hasattr(sys, '_MEIPASS')

    # Nuitka detection - check for specific Nuitka environment variables or paths
    nuitka_bundled = False
    if hasattr(sys, 'meta_path'):
        for finder in sys.meta_path:
            if 'nuitka' in str(finder).lower():
                nuitka_bundled = True
                break

    # Additional check for Nuitka - look for __compiled__ attribute
    nuitka_compiled = hasattr(sys, '__compiled__')

    return pyinstaller_bundled or nuitka_bundled or nuitka_compiled

def get_base_dir():
    """
    Get the base directory for the application.
    Works in both development and bundled environments.

    Returns:
        str: The base directory path
    """
    if is_bundled():
        # For PyInstaller
        if hasattr(sys, '_MEIPASS'):
            return sys._MEIPASS

        # For Nuitka, use the directory containing the executable
        return os.path.dirname(sys.executable)
    else:
        # In development mode, use the project root directory
        return os.path.dirname(os.path.dirname(os.path.abspath(__file__)))

def resolve_template_path(template_path):
    """
    Resolve a template path to work in both development and bundled environments.

    Args:
        template_path (str): The template path to resolve

    Returns:
        str: The resolved template path
    """
    # If we're running in a bundled environment
    if is_bundled():
        # Remove any leading path separators
        if template_path.startswith('/'):
            template_path = template_path[1:]
        elif template_path.startswith('\\'):
            template_path = template_path[1:] if template_path.startswith('\\\\') else template_path

        # Extract the filename and check if it's a Chubb template
        filename = os.path.basename(template_path)
        is_chubb = "chubb" in filename.lower() or "Chubb" in filename

        # Determine template type (dfir or bec)
        template_type = None
        if "bec" in template_path.lower():
            template_type = "bec"
        elif "dfir" in template_path.lower() or "ir" in template_path.lower():
            template_type = "dfir"

        # Try multiple possible locations for templates, prioritizing base_templates
        possible_paths = []

        # First priority: exact path in base_templates
        if "base_templates" in template_path:
            possible_paths.append(os.path.join(get_base_dir(), template_path))

        # Second priority: base_templates with correct subdirectory
        if template_type:
            possible_paths.append(os.path.join(get_base_dir(), 'templates', 'base_templates', template_type, filename))

        # Third priority: base_templates directory directly
        possible_paths.append(os.path.join(get_base_dir(), 'templates', 'base_templates', filename))

        # Last resort: original path
        possible_paths.append(os.path.join(get_base_dir(), template_path))

        # Check each possible path
        for path in possible_paths:
            if os.path.exists(path):
                print(f"Found template at: {path}")
                return path

        # If we get here, we couldn't find the template
        print(f"WARNING: Could not find template at any of these locations:")
        for path in possible_paths:
            print(f"  - {path}")

        # Return the standard path anyway, for logging purposes
        return os.path.join(get_base_dir(), template_path)
    else:
        # In development mode, use the path as is
        return template_path

def get_all_template_files():
    """
    Get a list of all template files in the templates directory.
    Useful for debugging template issues.

    Returns:
        list: List of template file paths
    """
    template_dir = os.path.join(get_base_dir(), 'templates')
    template_files = []

    if os.path.exists(template_dir):
        for root, _, files in os.walk(template_dir):
            for file in files:
                if file.endswith('.docx'):
                    template_files.append(os.path.join(root, file))

    return template_files
