"""
Simple address widget for address input.
"""

from PySide6.QtWidgets import <PERSON>Widget, QLineEdit, QHBoxLayout
from PySide6.QtCore import Signal

class AddressWidget(QWidget):
    """
    Simple widget for address input.
    """

    # Signal emitted when an address is selected
    address_selected = Signal(str)

    def __init__(self, parent=None):
        """
        Initialize the address widget.

        Args:
            parent: The parent widget
        """
        super().__init__(parent)
        self.init_ui()

    def init_ui(self):
        """
        Initialize the UI.
        """
        # Create the main layout
        main_layout = QHBoxLayout(self)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)

        # Create the address input field
        self.address_input = QLineEdit()
        self.address_input.setPlaceholderText("Enter address...")
        self.address_input.returnPressed.connect(self.on_return_pressed)
        main_layout.addWidget(self.address_input)

        # Set the layout
        self.setLayout(main_layout)

    def text(self):
        """
        Get the current text in the address input field.

        Returns:
            The current text
        """
        return self.address_input.text()

    def setText(self, text):
        """
        Set the text in the address input field.

        Args:
            text: The text to set
        """
        self.address_input.setText(text)

    def on_return_pressed(self):
        """
        Handle when the user presses Enter in the address input field.
        """
        # Get the current text
        text = self.address_input.text()

        # Emit the address_selected signal
        self.address_selected.emit(text)

    def clear(self):
        """
        Clear the address input field.
        """
        self.address_input.clear()
