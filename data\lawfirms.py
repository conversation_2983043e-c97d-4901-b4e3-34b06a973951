"""
Law firm data for PACE application.
"""

# List of law firms
LAW_FIRMS = [
    "No Counsel",
    "Akerman, LLP",
    "Allotta Farley Co., LPA",
    "Atheria Law",
    "Baird Holm",
    "Baker & Hostetler LLP",
    "Baker Botts",
    "Baker Donelson",
    "Beckstead Terry PLLC",
    "Burke Cromer Cremonese LLC",
    "Caplan & Earnest LLC",
    "Carlton Fields, P.A.",
    "Cipriani & Werner PC",
    "Clark Hill PLC",
    "Connell Foley LLP",
    "Constangy, Brooks, Smith & Prophete, LLP",
    "Cooley LLP",
    "Copeland Stair Valz & Lovell LLP",
    "Covington & Burling LLC",
    "Cozen O'Connor",
    "Cullen Dykman LLP",
    "Debevoise & Plimpton",
    "Denton",
    "DLA Piper",
    "Doyle Schafer McMahon",
    "Dykema Gossett PLLC",
    "Eckert Seamans Cherin & Mellott, LLC",
    "Farrell Fritz",
    "Fischer Law, LLC",
    "FisherBroyles, LLP",
    "Foley & Lardner LLP",
    "Fox Rothschild LLP",
    "Freeman Mathis & Gary, LLP",
    "<PERSON>ermer PLLC",
    "Gibbons P.C.",
    "Godfrey & Kahn SC",
    "Goodwin Proctor",
    "<PERSON>, LLP",
    "Gray Legal",
    "Greenberg Tra<PERSON>g, LLP",
    "Hall Booth Smith, PC",
    "Hall Estill",
    "Hall, Render, Killian, Heath & Lyman, P.C.",
    "Hand Arendall Harrison Sale",
    "Hogan Lovells",
    "Holland & Knight",
    "Holland & Knight, LLP",
    "Howard & Howard",
    "Hunton Andrews Kurth",
    "Husch Blackwell",
    "Jackson Lewis PC",
    "Kaufman & Canoles PC",
    "Kaufman Dolowich & Voluck, LLP",
    "Kennedy's Law",
    "Kennedys Law LLP",
    "King & Spalding LLP",
    "Kraw Law Group / Weinberg, Roger & Rosenfeld",
    "Law Office of Danielle K. Beland-Allard, PLLC",
    "Lewis Brisbois Bisgaard & Smith, LLP",
    "Lippes Mathias LLP",
    "Locke Lord, LLP",
    "Loeb & Loeb, LLP",
    "Margolis Edelstein",
    "Marshall Dennehey",
    "Maynard Nexsen PC",
    "McDonald Hopkins LLC",
    "Mullen Coughlin LLC",
    "Nelson Mullins Riley & Scarborough, LLP",
    "Nexsen Pruet",
    "Nixon Peabody LLP",
    "Norton Rose Fulbright",
    "Octillo Law",
    "Ogletree Deakins",
    "Parker Poe",
    "Parks Zeigler, PLLC",
    "Phelps Dunbar LLP",
    "Pierson Ferdinand LLP",
    "Polsinelli PC",
    "Porter Wright",
    "Ray Quinney & Nebeker PC",
    "Renzulli Law Firm LLP",
    "Ritter Gallagher",
    "Roetzel & Andress",
    "Ropes & Gray LLP",
    "Ruskin Moscou Faltischek",
    "Savage Law Partners, LLP",
    "Sheppard, Mullin, Richter & Hampton, LLP",
    "Shook, Hardy & Bacon, LLP",
    "Sidley Austin LLP",
    "Spencer Fane LLP",
    "Stinson LLP",
    "STOLL KEENON OGDEN PLLC",
    "The Beckage Firm",
    "Tressler LLP",
    "Troutman Pepper Locke LLP",
    "VedderPrice",
    "Vorys, Sater, Seymour and Pease LLP",
    "Weissmann Zucker Euster + Katz P.C.",
    "Whitelaw Twining LLP",
    "Wilks Law",
    "Wilson, Elser, Moskowitz, Edelman & Dicker LLP",
    "Winget, Spadafora & Schwartzberg",
    "Womble Bond Dickenson, LLC",
    "Wood Smith Henning & Berman LLP",
    "Woods Rogers PLC",
    "Wyche, P.A."
]

# Special case law firms that have unique templates
SPECIAL_CASE_LAW_FIRMS = {
    "Baker & Hostetler LLP": {
        "BEC": True,
        "DFIR": False,
        "TACI": False,
        "RR": False
    },
    "The Beckage Firm": {
        "BEC": False,
        "DFIR": True,
        "TACI": True,
        "RR": False
    },
    "Dykema Gossett PLLC": {
        "BEC": True,
        "DFIR": False,
        "TACI": False,
        "RR": False
    },
    "Woods Rogers PLC": {
        "BEC": True,
        "DFIR": False,
        "TACI": False,
        "RR": False
    }
}

def get_law_firm_names():
    """Return a sorted list of law firm names for the dropdown menu."""
    return sorted(LAW_FIRMS)

def is_special_case_law_firm(law_firm, engagement_type):
    """
    Check if the law firm has a special template for the given engagement type.
    
    Args:
        law_firm (str): The name of the law firm
        engagement_type (str): The type of engagement (BEC, DFIR, TACI, RR)
        
    Returns:
        bool: True if the law firm has a special template for the engagement type
    """
    if law_firm in SPECIAL_CASE_LAW_FIRMS:
        return SPECIAL_CASE_LAW_FIRMS[law_firm].get(engagement_type, False)
    return False
