PACE Placeholder Audit - 2025-08-18T12:28:59

Template: templates\base_templates\baa\Template_Business_Associate_Agreement.docx
  Placeholders found: 3  Matched: 1  Missing: 2
  Missing:
    - </w:t></w:r><w:proofErr w:type="spellStart"/><w:r w:rsidR="00E275AE"><w:rPr><w:rFonts w:asciiTheme="minorHAnsi" w:hAnsiTheme="minorHAnsi" w:cstheme="minorBidi"/><w:color w:val="000000" w:themeColor="text1"/><w:sz w:val="22"/><w:szCs w:val="22"/></w:rPr><w:t>client_address</w:t></w:r><w:proofErr w:type="spellEnd"/><w:r w:rsidR="00E275AE"><w:rPr><w:rFonts w:asciiTheme="minorHAnsi" w:hAnsiTheme="minorHAnsi" w:cstheme="minorBidi"/><w:color w:val="000000" w:themeColor="text1"/><w:sz w:val="22"/><w:szCs w:val="22"/></w:rPr><w:t>
    - </w:t></w:r><w:proofErr w:type="spellStart"/><w:r w:rsidR="00E275AE"><w:rPr><w:rFonts w:asciiTheme="minorHAnsi" w:hAnsiTheme="minorHAnsi" w:cstheme="minorBidi"/><w:sz w:val="22"/><w:szCs w:val="22"/></w:rPr><w:t>formatted_date</w:t></w:r><w:proofErr w:type="spellEnd"/><w:r w:rsidR="00E275AE"><w:rPr><w:rFonts w:asciiTheme="minorHAnsi" w:hAnsiTheme="minorHAnsi" w:cstheme="minorBidi"/><w:sz w:val="22"/><w:szCs w:val="22"/></w:rPr><w:t>
  Matched (sample values):
    - client = <docxtpl.richtext.RichText object at 0x000002F430604D70>

Template: templates\base_templates\bec\SOW_Baker_GCP_BEC_template.docx
  Placeholders found: 9  Matched: 3  Missing: 6
  Missing:
    - </w:t></w:r><w:proofErr w:type="gramEnd"/><w:r w:rsidRPr="00A97158"><w:rPr><w:b/><w:bCs/><w:sz w:val="24"/><w:szCs w:val="24"/></w:rPr><w:t>baker_gcp_fixed_cost_rate
    - </w:t></w:r><w:proofErr w:type="gramEnd"/><w:r w:rsidRPr="1C1978AE"><w:rPr><w:sz w:val="24"/><w:szCs w:val="24"/></w:rPr><w:t>baker_gcp_tenant_analysis
    - </w:t></w:r><w:proofErr w:type="spellStart"/><w:proofErr w:type="gramEnd"/><w:r w:rsidRPr="1C1978AE"><w:rPr><w:b/><w:bCs/><w:sz w:val="24"/><w:szCs w:val="24"/></w:rPr><w:t>baker_gcp_total_cost</w:t></w:r><w:proofErr w:type="spellEnd"/><w:r w:rsidRPr="1C1978AE"><w:rPr><w:b/><w:bCs/><w:sz w:val="24"/><w:szCs w:val="24"/></w:rPr><w:t>
    - baker_gcp_fixed_cost_rate
    - baker_gcp_num_users
    - baker_gcp_triage_num
  Matched (sample values):
    - Client = <docxtpl.richtext.RichText object at 0x000002F4306AFE10>
    - client = <docxtpl.richtext.RichText object at 0x000002F4306AFE10>
    - formatted_date = 'August 18, 2025'

Template: templates\base_templates\bec\SOW_Baker_M365_BEC_template.docx
  Placeholders found: 10  Matched: 4  Missing: 6
  Missing:
    - </w:t></w:r><w:proofErr w:type="gramEnd"/><w:r w:rsidR="00441BAC" w:rsidRPr="00441BAC"><w:rPr><w:rFonts w:cstheme="minorHAnsi"/><w:sz w:val="24"/><w:szCs w:val="24"/></w:rPr><w:t>baker_m365_message_extraction
    - </w:t></w:r><w:proofErr w:type="gramEnd"/><w:r w:rsidR="00441BAC" w:rsidRPr="00441BAC"><w:rPr><w:rFonts w:eastAsiaTheme="minorEastAsia"/><w:b/><w:bCs/><w:sz w:val="24"/><w:szCs w:val="24"/></w:rPr><w:t>baker_m365_fixed_cost_rate</w:t></w:r><w:r w:rsidR="00441BAC"><w:rPr><w:rFonts w:eastAsiaTheme="minorEastAsia"/><w:b/><w:bCs/><w:sz w:val="24"/><w:szCs w:val="24"/></w:rPr><w:t>
    - </w:t></w:r><w:proofErr w:type="gramEnd"/><w:r w:rsidR="00441BAC" w:rsidRPr="00441BAC"><w:rPr><w:rFonts w:eastAsiaTheme="minorEastAsia"/><w:b/><w:bCs/><w:sz w:val="24"/><w:szCs w:val="24"/></w:rPr><w:t>baker_m365_message_extraction
    - </w:t></w:r><w:proofErr w:type="spellStart"/><w:r w:rsidR="008F4C0D"><w:rPr><w:rFonts w:ascii="Calibri" w:eastAsiaTheme="minorEastAsia" w:hAnsi="Calibri" w:cs="Calibri"/></w:rPr><w:t>formatted_date</w:t></w:r><w:proofErr w:type="spellEnd"/><w:r w:rsidR="008F4C0D"><w:rPr><w:rFonts w:ascii="Calibri" w:eastAsiaTheme="minorEastAsia" w:hAnsi="Calibri" w:cs="Calibri"/></w:rPr><w:t>
    - </w:t></w:r><w:proofErr w:type="spellStart"/><w:r w:rsidR="00D35691" w:rsidRPr="00D35691"><w:rPr><w:rFonts w:eastAsiaTheme="minorEastAsia"/><w:sz w:val="24"/><w:szCs w:val="24"/></w:rPr><w:t>triage_count</w:t></w:r><w:proofErr w:type="spellEnd"/><w:r w:rsidR="00D35691" w:rsidRPr="00D35691"><w:rPr><w:rFonts w:eastAsiaTheme="minorEastAsia"/><w:sz w:val="24"/><w:szCs w:val="24"/></w:rPr><w:t>
    - baker_m365_fixed_cost_</w:t></w:r><w:proofErr w:type="gramStart"/><w:r w:rsidR="00441BAC" w:rsidRPr="00441BAC"><w:rPr><w:rFonts w:asciiTheme="minorHAnsi" w:hAnsiTheme="minorHAnsi" w:cstheme="minorHAnsi"/></w:rPr><w:t>rate
  Matched (sample values):
    - baker_m365_num_mailboxes = '10'
    - baker_m365_num_users = '1,000'
    - baker_m365_tenant_analysis = '$7,500'
    - client = <docxtpl.richtext.RichText object at 0x000002F4306AFE10>

Template: templates\base_templates\bec\SOW_Chubb_Exchange_BEC_template.docx
  Placeholders found: 6  Matched: 2  Missing: 4
  Missing:
    - </w:t></w:r><w:proofErr w:type="spellStart"/><w:r w:rsidR="00CF2F4D"><w:rPr><w:rFonts w:ascii="Calibri" w:eastAsiaTheme="minorEastAsia" w:hAnsi="Calibri" w:cs="Calibri"/><w:sz w:val="24"/><w:szCs w:val="24"/></w:rPr><w:t>formatted_date</w:t></w:r><w:proofErr w:type="spellEnd"/><w:r w:rsidR="00CF2F4D"><w:rPr><w:rFonts w:ascii="Calibri" w:eastAsiaTheme="minorEastAsia" w:hAnsi="Calibri" w:cs="Calibri"/><w:sz w:val="24"/><w:szCs w:val="24"/></w:rPr><w:t>
    - </w:t></w:r><w:proofErr w:type="spellStart"/><w:r w:rsidR="00CF2F4D"><w:rPr><w:rFonts w:ascii="Calibri" w:eastAsiaTheme="minorEastAsia" w:hAnsi="Calibri" w:cs="Calibri"/><w:sz w:val="24"/><w:szCs w:val="24"/><w:highlight w:val="green"/></w:rPr><w:t>lawfirm</w:t></w:r><w:proofErr w:type="spellEnd"/><w:r w:rsidR="00CF2F4D"><w:rPr><w:rFonts w:ascii="Calibri" w:eastAsiaTheme="minorEastAsia" w:hAnsi="Calibri" w:cs="Calibri"/><w:sz w:val="24"/><w:szCs w:val="24"/><w:highlight w:val="green"/></w:rPr><w:t>
    - </w:t></w:r><w:proofErr w:type="spellStart"/><w:r w:rsidR="00CF2F4D"><w:rPr><w:sz w:val="24"/><w:szCs w:val="24"/></w:rPr><w:t>lawfirm</w:t></w:r><w:proofErr w:type="spellEnd"/><w:r w:rsidR="00CF2F4D"><w:rPr><w:sz w:val="24"/><w:szCs w:val="24"/></w:rPr><w:t>
    - </w:t></w:r><w:proofErr w:type="spellStart"/><w:r><w:rPr><w:rFonts w:cstheme="minorHAnsi"/><w:b/><w:bCs/><w:sz w:val="24"/><w:szCs w:val="24"/></w:rPr><w:t>Lawfirm</w:t></w:r><w:proofErr w:type="spellEnd"/><w:r><w:rPr><w:rFonts w:cstheme="minorHAnsi"/><w:b/><w:bCs/><w:sz w:val="24"/><w:szCs w:val="24"/></w:rPr><w:t>
  Matched (sample values):
    - Client = <docxtpl.richtext.RichText object at 0x000002F4306AFE10>
    - client = <docxtpl.richtext.RichText object at 0x000002F4306AFE10>

Template: templates\base_templates\bec\SOW_Chubb_GCP_BEC_template.docx
  Placeholders found: 32  Matched: 19  Missing: 13
  Missing:
    - </w:t></w:r><w:proofErr w:type="spellStart"/><w:r w:rsidR="00726E98" w:rsidRPr="00726E98"><w:rPr><w:rFonts w:ascii="Calibri" w:eastAsiaTheme="minorEastAsia" w:hAnsi="Calibri" w:cs="Calibri"/><w:sz w:val="24"/><w:szCs w:val="24"/></w:rPr><w:t>formatted_date</w:t></w:r><w:proofErr w:type="spellEnd"/><w:r w:rsidR="00726E98" w:rsidRPr="00726E98"><w:rPr><w:rFonts w:ascii="Calibri" w:eastAsiaTheme="minorEastAsia" w:hAnsi="Calibri" w:cs="Calibri"/><w:sz w:val="24"/><w:szCs w:val="24"/></w:rPr><w:t>
    - </w:t></w:r><w:proofErr w:type="spellStart"/><w:r w:rsidR="00726E98"><w:rPr><w:rFonts w:ascii="Calibri" w:eastAsiaTheme="minorEastAsia" w:hAnsi="Calibri" w:cs="Calibri"/><w:sz w:val="24"/><w:szCs w:val="24"/><w:highlight w:val="green"/></w:rPr><w:t>lawfirm</w:t></w:r><w:proofErr w:type="spellEnd"/><w:r w:rsidR="00726E98"><w:rPr><w:rFonts w:ascii="Calibri" w:eastAsiaTheme="minorEastAsia" w:hAnsi="Calibri" w:cs="Calibri"/><w:sz w:val="24"/><w:szCs w:val="24"/><w:highlight w:val="green"/></w:rPr><w:t>
    - </w:t></w:r><w:proofErr w:type="spellStart"/><w:r w:rsidR="00726E98"><w:rPr><w:sz w:val="24"/><w:szCs w:val="24"/></w:rPr><w:t>lawfirm</w:t></w:r><w:proofErr w:type="spellEnd"/><w:r w:rsidR="00726E98"><w:rPr><w:sz w:val="24"/><w:szCs w:val="24"/></w:rPr><w:t>
    - baker_gcp_fixed_cost_rate
    - baker_gcp_num_users
    - baker_gcp_tenant_analysis
    - baker_gcp_total_cost
    - baker_gcp_triage_num
    - fixed_gcp_cost_rate
    - gcp_tenant_analysis
    - gcp_total_cost
    - gcp_triage_num
    - num_gcp_users
  Matched (sample values):
    - baker_m365_fixed_cost_rate = '$7,500'
    - baker_m365_message_extraction = '$1,500'
    - baker_m365_num_mailboxes = '10'
    - baker_m365_num_users = '1,000'
    - baker_m365_tenant_analysis = '$7,500'
    - baker_m365_total_cost = '$9,000'
    - baker_m365_triage_num = '0'
    - bec_rate = '450'
    - client = <docxtpl.richtext.RichText object at 0x000002F4306AFE10>
    - client_address = <docxtpl.richtext.RichText object at 0x000002F4306AFA80>

Template: templates\base_templates\bec\SOW_Chubb_M365_BEC_template.docx
  Placeholders found: 29  Matched: 19  Missing: 10
  Missing:
    - baker_gcp_fixed_cost_rate
    - baker_gcp_num_users
    - baker_gcp_tenant_analysis
    - baker_gcp_total_cost
    - baker_gcp_triage_num
    - fixed_gcp_cost_rate
    - gcp_tenant_analysis
    - gcp_total_cost
    - gcp_triage_num
    - num_gcp_users
  Matched (sample values):
    - baker_m365_fixed_cost_rate = '$7,500'
    - baker_m365_message_extraction = '$1,500'
    - baker_m365_num_mailboxes = '10'
    - baker_m365_num_users = '1,000'
    - baker_m365_tenant_analysis = '$7,500'
    - baker_m365_total_cost = '$9,000'
    - baker_m365_triage_num = '0'
    - bec_rate = '450'
    - client = <docxtpl.richtext.RichText object at 0x000002F4306AFE10>
    - client_address = <docxtpl.richtext.RichText object at 0x000002F4306AFA80>

Template: templates\base_templates\bec\SOW_Coalition_GCP_BEC_template.docx
  Placeholders found: 29  Matched: 19  Missing: 10
  Missing:
    - baker_gcp_fixed_cost_rate
    - baker_gcp_num_users
    - baker_gcp_tenant_analysis
    - baker_gcp_total_cost
    - baker_gcp_triage_num
    - fixed_gcp_cost_rate
    - gcp_tenant_analysis
    - gcp_total_cost
    - gcp_triage_num
    - num_gcp_users
  Matched (sample values):
    - baker_m365_fixed_cost_rate = '$7,500'
    - baker_m365_message_extraction = '$1,500'
    - baker_m365_num_mailboxes = '10'
    - baker_m365_num_users = '1,000'
    - baker_m365_tenant_analysis = '$7,500'
    - baker_m365_total_cost = '$9,000'
    - baker_m365_triage_num = '0'
    - bec_rate = '450'
    - client = <docxtpl.richtext.RichText object at 0x000002F4306AFE10>
    - client_address = <docxtpl.richtext.RichText object at 0x000002F4306AFA80>

Template: templates\base_templates\bec\SOW_Coalition_M365_BEC_template.docx
  Placeholders found: 29  Matched: 19  Missing: 10
  Missing:
    - baker_gcp_fixed_cost_rate
    - baker_gcp_num_users
    - baker_gcp_tenant_analysis
    - baker_gcp_total_cost
    - baker_gcp_triage_num
    - fixed_gcp_cost_rate
    - gcp_tenant_analysis
    - gcp_total_cost
    - gcp_triage_num
    - num_gcp_users
  Matched (sample values):
    - baker_m365_fixed_cost_rate = '$7,500'
    - baker_m365_message_extraction = '$1,500'
    - baker_m365_num_mailboxes = '10'
    - baker_m365_num_users = '1,000'
    - baker_m365_tenant_analysis = '$7,500'
    - baker_m365_total_cost = '$9,000'
    - baker_m365_triage_num = '0'
    - bec_rate = '450'
    - client = <docxtpl.richtext.RichText object at 0x000002F4306AFE10>
    - client_address = <docxtpl.richtext.RichText object at 0x000002F4306AFA80>

Template: templates\base_templates\bec\SOW_Dykema_GCP_BEC_template.docx
  Placeholders found: 29  Matched: 19  Missing: 10
  Missing:
    - baker_gcp_fixed_cost_rate
    - baker_gcp_num_users
    - baker_gcp_tenant_analysis
    - baker_gcp_total_cost
    - baker_gcp_triage_num
    - fixed_gcp_cost_rate
    - gcp_tenant_analysis
    - gcp_total_cost
    - gcp_triage_num
    - num_gcp_users
  Matched (sample values):
    - baker_m365_fixed_cost_rate = '$7,500'
    - baker_m365_message_extraction = '$1,500'
    - baker_m365_num_mailboxes = '10'
    - baker_m365_num_users = '1,000'
    - baker_m365_tenant_analysis = '$7,500'
    - baker_m365_total_cost = '$9,000'
    - baker_m365_triage_num = '0'
    - bec_rate = '450'
    - client = <docxtpl.richtext.RichText object at 0x000002F4306AFE10>
    - client_address = <docxtpl.richtext.RichText object at 0x000002F4306AFA80>

Template: templates\base_templates\bec\SOW_Dykema_M365_BEC_template.docx
  Placeholders found: 29  Matched: 19  Missing: 10
  Missing:
    - baker_gcp_fixed_cost_rate
    - baker_gcp_num_users
    - baker_gcp_tenant_analysis
    - baker_gcp_total_cost
    - baker_gcp_triage_num
    - fixed_gcp_cost_rate
    - gcp_tenant_analysis
    - gcp_total_cost
    - gcp_triage_num
    - num_gcp_users
  Matched (sample values):
    - baker_m365_fixed_cost_rate = '$7,500'
    - baker_m365_message_extraction = '$1,500'
    - baker_m365_num_mailboxes = '10'
    - baker_m365_num_users = '1,000'
    - baker_m365_tenant_analysis = '$7,500'
    - baker_m365_total_cost = '$9,000'
    - baker_m365_triage_num = '0'
    - bec_rate = '450'
    - client = <docxtpl.richtext.RichText object at 0x000002F4306AFE10>
    - client_address = <docxtpl.richtext.RichText object at 0x000002F4306AFA80>

Template: templates\base_templates\bec\SOW_Exchange_BEC_template.docx
  Placeholders found: 29  Matched: 19  Missing: 10
  Missing:
    - baker_gcp_fixed_cost_rate
    - baker_gcp_num_users
    - baker_gcp_tenant_analysis
    - baker_gcp_total_cost
    - baker_gcp_triage_num
    - fixed_gcp_cost_rate
    - gcp_tenant_analysis
    - gcp_total_cost
    - gcp_triage_num
    - num_gcp_users
  Matched (sample values):
    - baker_m365_fixed_cost_rate = '$7,500'
    - baker_m365_message_extraction = '$1,500'
    - baker_m365_num_mailboxes = '10'
    - baker_m365_num_users = '1,000'
    - baker_m365_tenant_analysis = '$7,500'
    - baker_m365_total_cost = '$9,000'
    - baker_m365_triage_num = '0'
    - bec_rate = '450'
    - client = <docxtpl.richtext.RichText object at 0x000002F4306AFE10>
    - client_address = <docxtpl.richtext.RichText object at 0x000002F4306AFA80>

Template: templates\base_templates\bec\SOW_GCP_BEC_template.docx
  Placeholders found: 29  Matched: 19  Missing: 10
  Missing:
    - baker_gcp_fixed_cost_rate
    - baker_gcp_num_users
    - baker_gcp_tenant_analysis
    - baker_gcp_total_cost
    - baker_gcp_triage_num
    - fixed_gcp_cost_rate
    - gcp_tenant_analysis
    - gcp_total_cost
    - gcp_triage_num
    - num_gcp_users
  Matched (sample values):
    - baker_m365_fixed_cost_rate = '$7,500'
    - baker_m365_message_extraction = '$1,500'
    - baker_m365_num_mailboxes = '10'
    - baker_m365_num_users = '1,000'
    - baker_m365_tenant_analysis = '$7,500'
    - baker_m365_total_cost = '$9,000'
    - baker_m365_triage_num = '0'
    - bec_rate = '450'
    - client = <docxtpl.richtext.RichText object at 0x000002F4306AFE10>
    - client_address = <docxtpl.richtext.RichText object at 0x000002F4306AFA80>

Template: templates\base_templates\bec\SOW_M365_BEC_template.docx
  Placeholders found: 29  Matched: 19  Missing: 10
  Missing:
    - baker_gcp_fixed_cost_rate
    - baker_gcp_num_users
    - baker_gcp_tenant_analysis
    - baker_gcp_total_cost
    - baker_gcp_triage_num
    - fixed_gcp_cost_rate
    - gcp_tenant_analysis
    - gcp_total_cost
    - gcp_triage_num
    - num_gcp_users
  Matched (sample values):
    - baker_m365_fixed_cost_rate = '$7,500'
    - baker_m365_message_extraction = '$1,500'
    - baker_m365_num_mailboxes = '10'
    - baker_m365_num_users = '1,000'
    - baker_m365_tenant_analysis = '$7,500'
    - baker_m365_total_cost = '$9,000'
    - baker_m365_triage_num = '0'
    - bec_rate = '450'
    - client = <docxtpl.richtext.RichText object at 0x000002F4306AFE10>
    - client_address = <docxtpl.richtext.RichText object at 0x000002F4306AFA80>

Template: templates\base_templates\bec\SOW_Woods_M365_BEC_template.docx
  Placeholders found: 29  Matched: 19  Missing: 10
  Missing:
    - baker_gcp_fixed_cost_rate
    - baker_gcp_num_users
    - baker_gcp_tenant_analysis
    - baker_gcp_total_cost
    - baker_gcp_triage_num
    - fixed_gcp_cost_rate
    - gcp_tenant_analysis
    - gcp_total_cost
    - gcp_triage_num
    - num_gcp_users
  Matched (sample values):
    - baker_m365_fixed_cost_rate = '$7,500'
    - baker_m365_message_extraction = '$1,500'
    - baker_m365_num_mailboxes = '10'
    - baker_m365_num_users = '1,000'
    - baker_m365_tenant_analysis = '$7,500'
    - baker_m365_total_cost = '$9,000'
    - baker_m365_triage_num = '0'
    - bec_rate = '450'
    - client = <docxtpl.richtext.RichText object at 0x000002F4306AFE10>
    - client_address = <docxtpl.richtext.RichText object at 0x000002F4306AFA80>

Template: templates\base_templates\dfir\SOW_Fixed_Fee_IR_template.docx
  Placeholders found: 8  Matched: 7  Missing: 1
  Missing:
    - </w:t></w:r><w:r><w:rPr><w:noProof/><w:sz w:val="24"/><w:szCs w:val="24"/></w:rPr><w:t>ffp</w:t></w:r><w:r><w:rPr><w:noProof/><w:sz w:val="24"/><w:szCs w:val="24"/></w:rPr><w:t>_endpoint_count
  Matched (sample values):
    - Client = <docxtpl.richtext.RichText object at 0x000002F430604D70>
    - Lawfirm = <docxtpl.richtext.RichText object at 0x000002F4306ECCD0>
    - ffp_all_costs = '$60,000'
    - ffp_soc_fee = '$5,000'
    - ffp_total_cost = '$55,000'
    - formatted_date = 'August 18, 2025'
    - lawfirm = <docxtpl.richtext.RichText object at 0x000002F4306ECCD0>

Template: templates\base_templates\dfir\SOW_IR_BRAP_Beckage_Verbiage_Template.docx
  Placeholders found: 6  Matched: 3  Missing: 3
  Missing:
    - </w:t></w:r><w:r w:rsidR="00F43B4B" w:rsidRPr="00F43B4B"><w:rPr><w:rFonts w:ascii="Calibri" w:hAnsi="Calibri" w:cs="Calibri"/><w:b/><w:bCs/><w:noProof/><w:color w:val="000000" w:themeColor="text1"/><w:sz w:val="24"/><w:szCs w:val="24"/><w:highlight w:val="yellow"/></w:rPr><w:t xml:space="preserve"> </w:t></w:r><w:r w:rsidR="00F43B4B"><w:rPr><w:rFonts w:ascii="Calibri" w:hAnsi="Calibri" w:cs="Calibri"/><w:b/><w:bCs/><w:noProof/><w:color w:val="000000" w:themeColor="text1"/><w:sz w:val="24"/><w:szCs w:val="24"/><w:highlight w:val="yellow"/></w:rPr><w:t>ffp_all_costs</w:t></w:r><w:r w:rsidR="00F43B4B"><w:rPr><w:rFonts w:ascii="Calibri" w:hAnsi="Calibri" w:cs="Calibri"/><w:b/><w:bCs/><w:noProof/><w:color w:val="000000" w:themeColor="text1"/><w:sz w:val="24"/><w:szCs w:val="24"/></w:rPr><w:t>
    - </w:t></w:r><w:r w:rsidR="00F43B4B" w:rsidRPr="00F43B4B"><w:rPr><w:rFonts w:ascii="Calibri" w:hAnsi="Calibri" w:cs="Calibri"/><w:b/><w:bCs/><w:noProof/><w:color w:val="000000" w:themeColor="text1"/><w:sz w:val="24"/><w:szCs w:val="24"/><w:highlight w:val="yellow"/></w:rPr><w:t xml:space="preserve"> </w:t></w:r><w:r w:rsidR="00F43B4B"><w:rPr><w:rFonts w:ascii="Calibri" w:hAnsi="Calibri" w:cs="Calibri"/><w:b/><w:bCs/><w:noProof/><w:color w:val="000000" w:themeColor="text1"/><w:sz w:val="24"/><w:szCs w:val="24"/><w:highlight w:val="yellow"/></w:rPr><w:t>ffp_soc_fee</w:t></w:r><w:r w:rsidR="00F43B4B"><w:rPr><w:rFonts w:ascii="Calibri" w:hAnsi="Calibri" w:cs="Calibri"/><w:b/><w:bCs/><w:noProof/><w:color w:val="000000" w:themeColor="text1"/><w:sz w:val="24"/><w:szCs w:val="24"/></w:rPr><w:t>
    - </w:t></w:r><w:r w:rsidR="00F43B4B" w:rsidRPr="00F43B4B"><w:rPr><w:rFonts w:ascii="Calibri" w:hAnsi="Calibri" w:cs="Calibri"/><w:b/><w:bCs/><w:noProof/><w:sz w:val="24"/><w:szCs w:val="24"/></w:rPr><w:t xml:space="preserve"> </w:t></w:r><w:r w:rsidR="00F43B4B"><w:rPr><w:rFonts w:ascii="Calibri" w:hAnsi="Calibri" w:cs="Calibri"/><w:b/><w:bCs/><w:noProof/><w:sz w:val="24"/><w:szCs w:val="24"/></w:rPr><w:t>ffp_total_cost</w:t></w:r><w:r w:rsidR="00F43B4B"><w:rPr><w:rFonts w:ascii="Calibri" w:hAnsi="Calibri" w:cs="Calibri"/><w:b/><w:bCs/><w:noProof/><w:sz w:val="24"/><w:szCs w:val="24"/></w:rPr><w:t>
  Matched (sample values):
    - CLIENT = <docxtpl.richtext.RichText object at 0x000002F430604D70>
    - client = <docxtpl.richtext.RichText object at 0x000002F430604D70>
    - formatted_date = 'August 18, 2025'

Template: templates\base_templates\dfir\SOW_IR_Template_IR Investigation_Beckage_Template.docx
  Placeholders found: 22  Matched: 2  Missing: 20
  Missing:
    - </w:t></w:r><w:proofErr w:type="gramEnd"/><w:r w:rsidR="009B5727" w:rsidRPr="006E6F39"><w:rPr><w:noProof/><w:color w:val="000000" w:themeColor="text1"/><w:sz w:val="24"/><w:szCs w:val="24"/></w:rPr><w:t>phase6_sp_costs</w:t></w:r><w:r w:rsidR="009B5727"><w:rPr><w:noProof/><w:color w:val="000000" w:themeColor="text1"/><w:sz w:val="24"/><w:szCs w:val="24"/></w:rPr><w:t>
    - </w:t></w:r><w:proofErr w:type="gramEnd"/><w:r w:rsidR="009B5727" w:rsidRPr="00E33923"><w:rPr><w:noProof/><w:sz w:val="24"/><w:szCs w:val="24"/></w:rPr><w:t>phase3_sp_costs</w:t></w:r><w:r w:rsidR="009B5727"><w:rPr><w:noProof/><w:sz w:val="24"/><w:szCs w:val="24"/></w:rPr><w:t>
    - </w:t></w:r><w:proofErr w:type="gramEnd"/><w:r w:rsidR="009B5727" w:rsidRPr="00E33923"><w:rPr><w:noProof/><w:sz w:val="24"/><w:szCs w:val="24"/></w:rPr><w:t>phase3_sp_hours</w:t></w:r><w:r w:rsidR="009B5727"><w:rPr><w:noProof/><w:sz w:val="24"/><w:szCs w:val="24"/></w:rPr><w:t>
    - </w:t></w:r><w:proofErr w:type="gramEnd"/><w:r w:rsidR="009B5727" w:rsidRPr="00E33923"><w:rPr><w:noProof/><w:sz w:val="24"/><w:szCs w:val="24"/></w:rPr><w:t>phase4_sp_costs</w:t></w:r><w:r w:rsidR="009B5727"><w:rPr><w:noProof/><w:sz w:val="24"/><w:szCs w:val="24"/></w:rPr><w:t>
    - </w:t></w:r><w:proofErr w:type="gramEnd"/><w:r w:rsidR="009B5727" w:rsidRPr="00E33923"><w:rPr><w:noProof/><w:sz w:val="24"/><w:szCs w:val="24"/></w:rPr><w:t>phase4_sp_hours</w:t></w:r><w:r w:rsidR="009B5727"><w:rPr><w:noProof/><w:sz w:val="24"/><w:szCs w:val="24"/></w:rPr><w:t>
    - </w:t></w:r><w:proofErr w:type="spellStart"/><w:proofErr w:type="gramEnd"/><w:r w:rsidR="009B5727" w:rsidRPr="006E6F39"><w:rPr><w:b/><w:bCs/><w:noProof/><w:color w:val="000000" w:themeColor="text1"/><w:sz w:val="24"/><w:szCs w:val="24"/></w:rPr><w:t>sp_edr_fee</w:t></w:r><w:proofErr w:type="spellEnd"/><w:r w:rsidR="009B5727"><w:rPr><w:b/><w:bCs/><w:noProof/><w:color w:val="000000" w:themeColor="text1"/><w:sz w:val="24"/><w:szCs w:val="24"/></w:rPr><w:t>
    - </w:t></w:r><w:proofErr w:type="spellStart"/><w:proofErr w:type="gramEnd"/><w:r w:rsidR="009B5727" w:rsidRPr="006E6F39"><w:rPr><w:b/><w:bCs/><w:noProof/><w:color w:val="000000" w:themeColor="text1"/><w:sz w:val="24"/><w:szCs w:val="24"/></w:rPr><w:t>sp_total_costs</w:t></w:r><w:proofErr w:type="spellEnd"/><w:r w:rsidR="009B5727"><w:rPr><w:b/><w:bCs/><w:noProof/><w:color w:val="000000" w:themeColor="text1"/><w:sz w:val="24"/><w:szCs w:val="24"/></w:rPr><w:t>
    - </w:t></w:r><w:proofErr w:type="spellStart"/><w:r w:rsidR="009B5727" w:rsidRPr="009B5727"><w:rPr><w:sz w:val="24"/><w:szCs w:val="24"/></w:rPr><w:t>formatted_date</w:t></w:r><w:proofErr w:type="spellEnd"/><w:r w:rsidR="009B5727" w:rsidRPr="009B5727"><w:rPr><w:sz w:val="24"/><w:szCs w:val="24"/></w:rPr><w:t>
    - </w:t></w:r><w:proofErr w:type="spellStart"/><w:r w:rsidR="009B5727" w:rsidRPr="00E33923"><w:rPr><w:noProof/><w:sz w:val="24"/><w:szCs w:val="24"/></w:rPr><w:t>sp_endpoint_count</w:t></w:r><w:proofErr w:type="spellEnd"/><w:r w:rsidR="009B5727"><w:rPr><w:noProof/><w:sz w:val="24"/><w:szCs w:val="24"/></w:rPr><w:t>
    - </w:t></w:r><w:proofErr w:type="spellStart"/><w:r w:rsidR="009B5727"><w:rPr><w:b/><w:bCs/><w:color w:val="000000" w:themeColor="text1"/><w:sz w:val="24"/><w:szCs w:val="24"/></w:rPr><w:t>sp_costs</w:t></w:r><w:proofErr w:type="spellEnd"/><w:r w:rsidR="009B5727"><w:rPr><w:b/><w:bCs/><w:color w:val="000000" w:themeColor="text1"/><w:sz w:val="24"/><w:szCs w:val="24"/></w:rPr><w:t>
    - </w:t></w:r><w:proofErr w:type="spellStart"/><w:r w:rsidR="009B5727"><w:rPr><w:b/><w:bCs/><w:noProof/><w:color w:val="000000" w:themeColor="text1"/><w:sz w:val="24"/><w:szCs w:val="24"/></w:rPr><w:t>sp_total_hours</w:t></w:r><w:proofErr w:type="spellEnd"/><w:r w:rsidR="009B5727"><w:rPr><w:b/><w:bCs/><w:noProof/><w:color w:val="000000" w:themeColor="text1"/><w:sz w:val="24"/><w:szCs w:val="24"/></w:rPr><w:t>
    - </w:t></w:r><w:proofErr w:type="spellStart"/><w:r w:rsidR="009B5727"><w:rPr><w:sz w:val="24"/><w:szCs w:val="24"/></w:rPr><w:t>formatted_date</w:t></w:r><w:proofErr w:type="spellEnd"/><w:r w:rsidR="009B5727"><w:rPr><w:sz w:val="24"/><w:szCs w:val="24"/></w:rPr><w:t>
    - </w:t></w:r><w:proofErr w:type="spellStart"/><w:r><w:rPr><w:color w:val="231F20"/><w:sz w:val="19"/><w:szCs w:val="19"/></w:rPr><w:t>formatted_date</w:t></w:r><w:proofErr w:type="spellEnd"/><w:r><w:rPr><w:color w:val="231F20"/><w:sz w:val="19"/><w:szCs w:val="19"/></w:rPr><w:t>
    - </w:t></w:r><w:proofErr w:type="spellStart"/><w:r><w:rPr><w:rFonts w:ascii="Calibri" w:hAnsi="Calibri" w:cs="Calibri"/><w:b/><w:bCs/><w:sz w:val="24"/><w:szCs w:val="24"/></w:rPr><w:t>lawfirm</w:t></w:r><w:proofErr w:type="spellEnd"/><w:r><w:rPr><w:rFonts w:ascii="Calibri" w:hAnsi="Calibri" w:cs="Calibri"/><w:b/><w:bCs/><w:sz w:val="24"/><w:szCs w:val="24"/></w:rPr><w:t>
    - </w:t></w:r><w:r w:rsidR="009B5727" w:rsidRPr="009B5727"><w:rPr><w:noProof/><w:sz w:val="24"/><w:szCs w:val="24"/></w:rPr><w:t xml:space="preserve"> </w:t></w:r><w:r w:rsidR="009B5727" w:rsidRPr="00BB2D35"><w:rPr><w:noProof/><w:sz w:val="24"/><w:szCs w:val="24"/></w:rPr><w:t>phase2_sp_costs</w:t></w:r><w:r w:rsidR="009B5727"><w:rPr><w:noProof/><w:sz w:val="24"/><w:szCs w:val="24"/></w:rPr><w:t>
    - </w:t></w:r><w:r w:rsidR="009B5727" w:rsidRPr="009B5727"><w:rPr><w:noProof/><w:sz w:val="24"/><w:szCs w:val="24"/></w:rPr><w:t xml:space="preserve"> </w:t></w:r><w:r w:rsidR="009B5727" w:rsidRPr="00BB2D35"><w:rPr><w:noProof/><w:sz w:val="24"/><w:szCs w:val="24"/></w:rPr><w:t>phase2_sp_hours</w:t></w:r><w:r w:rsidR="009B5727"><w:rPr><w:noProof/><w:sz w:val="24"/><w:szCs w:val="24"/></w:rPr><w:t>
    - </w:t></w:r><w:r w:rsidR="009B5727" w:rsidRPr="009B5727"><w:rPr><w:noProof/><w:sz w:val="24"/><w:szCs w:val="24"/></w:rPr><w:t xml:space="preserve"> </w:t></w:r><w:r w:rsidR="009B5727" w:rsidRPr="00E33923"><w:rPr><w:noProof/><w:sz w:val="24"/><w:szCs w:val="24"/></w:rPr><w:t>phase1_sp_costs</w:t></w:r><w:r w:rsidR="009B5727"><w:rPr><w:noProof/><w:sz w:val="24"/><w:szCs w:val="24"/></w:rPr><w:t>
    - </w:t></w:r><w:r w:rsidR="009B5727" w:rsidRPr="009B5727"><w:rPr><w:noProof/><w:sz w:val="24"/><w:szCs w:val="24"/></w:rPr><w:t xml:space="preserve"> </w:t></w:r><w:r w:rsidR="009B5727" w:rsidRPr="00E33923"><w:rPr><w:noProof/><w:sz w:val="24"/><w:szCs w:val="24"/></w:rPr><w:t>phase1_sp_hours</w:t></w:r><w:r w:rsidR="009B5727"><w:rPr><w:noProof/><w:sz w:val="24"/><w:szCs w:val="24"/></w:rPr><w:t>
    - </w:t></w:r><w:r w:rsidR="009B5727" w:rsidRPr="00E33923"><w:rPr><w:noProof/><w:color w:val="000000" w:themeColor="text1"/><w:sz w:val="24"/><w:szCs w:val="24"/></w:rPr><w:t>phase5_sp_costs</w:t></w:r><w:r w:rsidR="009B5727"><w:rPr><w:noProof/><w:color w:val="000000" w:themeColor="text1"/><w:sz w:val="24"/><w:szCs w:val="24"/></w:rPr><w:t>
    - </w:t></w:r><w:r w:rsidR="009B5727"><w:rPr><w:noProof/><w:color w:val="000000" w:themeColor="text1"/><w:sz w:val="24"/><w:szCs w:val="24"/></w:rPr><w:t>phase5_sp_hours</w:t></w:r><w:r w:rsidR="009B5727"><w:rPr><w:noProof/><w:color w:val="000000" w:themeColor="text1"/><w:sz w:val="24"/><w:szCs w:val="24"/></w:rPr><w:t>
  Matched (sample values):
    - Client = <docxtpl.richtext.RichText object at 0x000002F430604D70>
    - client = <docxtpl.richtext.RichText object at 0x000002F430604D70>

Template: templates\base_templates\dfir\SOW_IR_Template_Tokio-Marine_Single Price_IR Investigation.docx
  Placeholders found: 0  Matched: 0  Missing: 0

Template: templates\base_templates\dfir\SOW_IR_Template_Woods_Rogers_Only_IR_Investigation.docx
  Placeholders found: 19  Matched: 16  Missing: 3
  Missing:
    - </w:t></w:r><w:r w:rsidR="00195E6C" w:rsidRPr="00195E6C"><w:rPr><w:rFonts w:cstheme="minorHAnsi"/><w:sz w:val="24"/><w:szCs w:val="24"/></w:rPr><w:t>phase3_sp_costs
    - </w:t></w:r><w:r w:rsidR="00195E6C" w:rsidRPr="00195E6C"><w:rPr><w:rFonts w:cstheme="minorHAnsi"/><w:sz w:val="24"/><w:szCs w:val="24"/></w:rPr><w:t>phase3_sp_hours
    - phase5_sp_</w:t></w:r><w:r w:rsidR="00195E6C" w:rsidRPr="00195E6C"><w:rPr><w:rFonts w:cstheme="minorHAnsi"/><w:color w:val="000000" w:themeColor="text1"/><w:sz w:val="24"/><w:szCs w:val="24"/></w:rPr><w:t>hours</w:t></w:r><w:r w:rsidR="00195E6C"><w:rPr><w:rFonts w:cstheme="minorHAnsi"/><w:color w:val="000000" w:themeColor="text1"/><w:sz w:val="24"/><w:szCs w:val="24"/></w:rPr><w:t>
  Matched (sample values):
    - Client = <docxtpl.richtext.RichText object at 0x000002F430604D70>
    - date = 'August 18, 2025'
    - phase1_sp_costs = '$0'
    - phase1_sp_hours = '0'
    - phase2_sp_costs = '$0'
    - phase2_sp_hours = '0'
    - phase4_sp_costs = '$0'
    - phase4_sp_hours = '0'
    - phase5_sp_costs = '$0'
    - phase6_sp_costs = '$0'

Template: templates\base_templates\dfir\SOW_IR_Template_single_price_IR_Investigation.docx
  Placeholders found: 22  Matched: 21  Missing: 1
  Missing:
    - </w:t></w:r><w:r w:rsidR="00D37EB1" w:rsidRPr="00BA4DDB"><w:rPr><w:rFonts w:eastAsiaTheme="minorEastAsia" w:cstheme="minorHAnsi"/><w:sz w:val="24"/><w:szCs w:val="24"/></w:rPr><w:t>client</w:t></w:r><w:r w:rsidR="00BA4DDB" w:rsidRPr="00BA4DDB"><w:rPr><w:rFonts w:eastAsiaTheme="minorEastAsia" w:cstheme="minorHAnsi"/><w:sz w:val="24"/><w:szCs w:val="24"/></w:rPr><w:t>
  Matched (sample values):
    - Client = <docxtpl.richtext.RichText object at 0x000002F430604D70>
    - Lawfirm = <docxtpl.richtext.RichText object at 0x000002F4306ECCD0>
    - dfir_rate = '$320'
    - formatted_date = 'August 18, 2025'
    - phase1_sp_costs = '$0'
    - phase1_sp_hours = '0'
    - phase2_sp_costs = '$0'
    - phase2_sp_hours = '0'
    - phase3_sp_costs = '$0'
    - phase3_sp_hours = '0'

Template: templates\base_templates\dfir\SOW__IR_template_Beazley_FFP.docx
  Placeholders found: 9  Matched: 1  Missing: 8
  Missing:
    - </w:t></w:r><w:proofErr w:type="spellStart"/><w:r w:rsidR="003F0D09"><w:rPr><w:rFonts w:ascii="Calibri" w:eastAsiaTheme="minorEastAsia" w:hAnsi="Calibri" w:cs="Calibri"/><w:sz w:val="24"/><w:szCs w:val="24"/><w:highlight w:val="green"/></w:rPr><w:t>lawfirm</w:t></w:r><w:proofErr w:type="spellEnd"/><w:r w:rsidR="003F0D09"><w:rPr><w:rFonts w:ascii="Calibri" w:eastAsiaTheme="minorEastAsia" w:hAnsi="Calibri" w:cs="Calibri"/><w:sz w:val="24"/><w:szCs w:val="24"/><w:highlight w:val="green"/></w:rPr><w:t>
    - </w:t></w:r><w:proofErr w:type="spellStart"/><w:r w:rsidR="003F7330"><w:rPr><w:sz w:val="24"/><w:szCs w:val="24"/></w:rPr><w:t>lawfirm</w:t></w:r><w:proofErr w:type="spellEnd"/><w:r w:rsidR="003F7330"><w:rPr><w:sz w:val="24"/><w:szCs w:val="24"/></w:rPr><w:t>
    - </w:t></w:r><w:proofErr w:type="spellStart"/><w:r><w:rPr><w:rFonts w:cstheme="minorHAnsi"/><w:b/><w:bCs/><w:sz w:val="24"/><w:szCs w:val="24"/></w:rPr><w:t>lawfirm</w:t></w:r><w:proofErr w:type="spellEnd"/><w:r><w:rPr><w:rFonts w:cstheme="minorHAnsi"/><w:b/><w:bCs/><w:sz w:val="24"/><w:szCs w:val="24"/></w:rPr><w:t>
    - </w:t></w:r><w:r w:rsidR="003F0D09" w:rsidRPr="003F0D09"><w:rPr><w:b/><w:bCs/><w:noProof/><w:color w:val="000000" w:themeColor="text1"/><w:sz w:val="24"/><w:szCs w:val="24"/></w:rPr><w:t xml:space="preserve"> </w:t></w:r><w:r w:rsidR="003F0D09" w:rsidRPr="00373245"><w:rPr><w:b/><w:bCs/><w:noProof/><w:color w:val="000000" w:themeColor="text1"/><w:sz w:val="24"/><w:szCs w:val="24"/></w:rPr><w:t>beazley</w:t></w:r><w:proofErr w:type="gramEnd"/><w:r w:rsidR="003F0D09" w:rsidRPr="00373245"><w:rPr><w:b/><w:bCs/><w:noProof/><w:color w:val="000000" w:themeColor="text1"/><w:sz w:val="24"/><w:szCs w:val="24"/></w:rPr><w:t>_edr_fee</w:t></w:r><w:r w:rsidR="003F0D09"><w:rPr><w:b/><w:bCs/><w:noProof/><w:color w:val="000000" w:themeColor="text1"/><w:sz w:val="24"/><w:szCs w:val="24"/></w:rPr><w:t>
    - </w:t></w:r><w:r w:rsidR="003F0D09" w:rsidRPr="003F0D09"><w:rPr><w:b/><w:bCs/><w:noProof/><w:color w:val="000000" w:themeColor="text1"/><w:sz w:val="24"/><w:szCs w:val="24"/></w:rPr><w:t xml:space="preserve"> </w:t></w:r><w:r w:rsidR="003F0D09" w:rsidRPr="00373245"><w:rPr><w:b/><w:bCs/><w:noProof/><w:color w:val="000000" w:themeColor="text1"/><w:sz w:val="24"/><w:szCs w:val="24"/></w:rPr><w:t>beazley</w:t></w:r><w:proofErr w:type="gramEnd"/><w:r w:rsidR="003F0D09" w:rsidRPr="00373245"><w:rPr><w:b/><w:bCs/><w:noProof/><w:color w:val="000000" w:themeColor="text1"/><w:sz w:val="24"/><w:szCs w:val="24"/></w:rPr><w:t>_ffp_est_cost</w:t></w:r><w:r w:rsidR="003F0D09"><w:rPr><w:b/><w:bCs/><w:noProof/><w:color w:val="000000" w:themeColor="text1"/><w:sz w:val="24"/><w:szCs w:val="24"/></w:rPr><w:t>
    - </w:t></w:r><w:r w:rsidR="003F0D09" w:rsidRPr="003F0D09"><w:rPr><w:b/><w:bCs/><w:noProof/><w:color w:val="000000" w:themeColor="text1"/><w:sz w:val="24"/><w:szCs w:val="24"/></w:rPr><w:t xml:space="preserve"> </w:t></w:r><w:r w:rsidR="003F0D09" w:rsidRPr="00373245"><w:rPr><w:b/><w:bCs/><w:noProof/><w:color w:val="000000" w:themeColor="text1"/><w:sz w:val="24"/><w:szCs w:val="24"/></w:rPr><w:t>beazley</w:t></w:r><w:proofErr w:type="gramEnd"/><w:r w:rsidR="003F0D09" w:rsidRPr="00373245"><w:rPr><w:b/><w:bCs/><w:noProof/><w:color w:val="000000" w:themeColor="text1"/><w:sz w:val="24"/><w:szCs w:val="24"/></w:rPr><w:t>_total_costs</w:t></w:r><w:r w:rsidR="003F0D09"><w:rPr><w:b/><w:bCs/><w:noProof/><w:color w:val="000000" w:themeColor="text1"/><w:sz w:val="24"/><w:szCs w:val="24"/></w:rPr><w:t>
    - </w:t></w:r><w:r w:rsidR="003F0D09" w:rsidRPr="003F0D09"><w:rPr><w:noProof/><w:sz w:val="24"/><w:szCs w:val="24"/></w:rPr><w:t xml:space="preserve"> </w:t></w:r><w:r w:rsidR="003F0D09" w:rsidRPr="00373245"><w:rPr><w:noProof/><w:sz w:val="24"/><w:szCs w:val="24"/></w:rPr><w:t>beazley</w:t></w:r><w:proofErr w:type="gramEnd"/><w:r w:rsidR="003F0D09" w:rsidRPr="00373245"><w:rPr><w:noProof/><w:sz w:val="24"/><w:szCs w:val="24"/></w:rPr><w:t>_ffp_endpoint_count</w:t></w:r><w:r w:rsidR="003F0D09"><w:rPr><w:noProof/><w:sz w:val="24"/><w:szCs w:val="24"/></w:rPr><w:t>
    - </w:t></w:r><w:r w:rsidR="003F0D09" w:rsidRPr="003F0D09"><w:rPr><w:rFonts w:ascii="Calibri" w:eastAsiaTheme="minorEastAsia" w:hAnsi="Calibri" w:cs="Calibri"/><w:noProof/><w:sz w:val="24"/><w:szCs w:val="24"/></w:rPr><w:t xml:space="preserve"> </w:t></w:r><w:r w:rsidR="003F0D09" w:rsidRPr="00373245"><w:rPr><w:rFonts w:ascii="Calibri" w:eastAsiaTheme="minorEastAsia" w:hAnsi="Calibri" w:cs="Calibri"/><w:noProof/><w:sz w:val="24"/><w:szCs w:val="24"/></w:rPr><w:t>formatted</w:t></w:r><w:proofErr w:type="gramEnd"/><w:r w:rsidR="003F0D09" w:rsidRPr="00373245"><w:rPr><w:rFonts w:ascii="Calibri" w:eastAsiaTheme="minorEastAsia" w:hAnsi="Calibri" w:cs="Calibri"/><w:noProof/><w:sz w:val="24"/><w:szCs w:val="24"/></w:rPr><w:t>_date</w:t></w:r><w:r w:rsidR="003F0D09"><w:rPr><w:rFonts w:ascii="Calibri" w:eastAsiaTheme="minorEastAsia" w:hAnsi="Calibri" w:cs="Calibri"/><w:noProof/><w:sz w:val="24"/><w:szCs w:val="24"/></w:rPr><w:t>
  Matched (sample values):
    - client = <docxtpl.richtext.RichText object at 0x000002F430604D70>

Template: templates\base_templates\dfir\SOW__IR_template_Beazley_FFP_EDR_New_Hybrid.docx
  Placeholders found: 9  Matched: 1  Missing: 8
  Missing:
    - </w:t></w:r><w:proofErr w:type="spellStart"/><w:r w:rsidR="003F0D09"><w:rPr><w:rFonts w:ascii="Calibri" w:eastAsiaTheme="minorEastAsia" w:hAnsi="Calibri" w:cs="Calibri"/><w:sz w:val="24"/><w:szCs w:val="24"/><w:highlight w:val="green"/></w:rPr><w:t>lawfirm</w:t></w:r><w:proofErr w:type="spellEnd"/><w:r w:rsidR="003F0D09"><w:rPr><w:rFonts w:ascii="Calibri" w:eastAsiaTheme="minorEastAsia" w:hAnsi="Calibri" w:cs="Calibri"/><w:sz w:val="24"/><w:szCs w:val="24"/><w:highlight w:val="green"/></w:rPr><w:t>
    - </w:t></w:r><w:proofErr w:type="spellStart"/><w:r w:rsidR="003F7330"><w:rPr><w:sz w:val="24"/><w:szCs w:val="24"/></w:rPr><w:t>lawfirm</w:t></w:r><w:proofErr w:type="spellEnd"/><w:r w:rsidR="003F7330"><w:rPr><w:sz w:val="24"/><w:szCs w:val="24"/></w:rPr><w:t>
    - </w:t></w:r><w:proofErr w:type="spellStart"/><w:r><w:rPr><w:rFonts w:cstheme="minorHAnsi"/><w:b/><w:bCs/><w:sz w:val="24"/><w:szCs w:val="24"/></w:rPr><w:t>lawfirm</w:t></w:r><w:proofErr w:type="spellEnd"/><w:r><w:rPr><w:rFonts w:cstheme="minorHAnsi"/><w:b/><w:bCs/><w:sz w:val="24"/><w:szCs w:val="24"/></w:rPr><w:t>
    - </w:t></w:r><w:r w:rsidR="003F0D09" w:rsidRPr="003F0D09"><w:rPr><w:b/><w:bCs/><w:noProof/><w:color w:val="000000" w:themeColor="text1"/><w:sz w:val="24"/><w:szCs w:val="24"/></w:rPr><w:t xml:space="preserve"> </w:t></w:r><w:r w:rsidR="003F0D09" w:rsidRPr="00373245"><w:rPr><w:b/><w:bCs/><w:noProof/><w:color w:val="000000" w:themeColor="text1"/><w:sz w:val="24"/><w:szCs w:val="24"/></w:rPr><w:t>beazley</w:t></w:r><w:proofErr w:type="gramEnd"/><w:r w:rsidR="003F0D09" w:rsidRPr="00373245"><w:rPr><w:b/><w:bCs/><w:noProof/><w:color w:val="000000" w:themeColor="text1"/><w:sz w:val="24"/><w:szCs w:val="24"/></w:rPr><w:t>_edr_fee</w:t></w:r><w:r w:rsidR="003F0D09"><w:rPr><w:b/><w:bCs/><w:noProof/><w:color w:val="000000" w:themeColor="text1"/><w:sz w:val="24"/><w:szCs w:val="24"/></w:rPr><w:t>
    - </w:t></w:r><w:r w:rsidR="003F0D09" w:rsidRPr="003F0D09"><w:rPr><w:b/><w:bCs/><w:noProof/><w:color w:val="000000" w:themeColor="text1"/><w:sz w:val="24"/><w:szCs w:val="24"/></w:rPr><w:t xml:space="preserve"> </w:t></w:r><w:r w:rsidR="003F0D09" w:rsidRPr="00373245"><w:rPr><w:b/><w:bCs/><w:noProof/><w:color w:val="000000" w:themeColor="text1"/><w:sz w:val="24"/><w:szCs w:val="24"/></w:rPr><w:t>beazley</w:t></w:r><w:proofErr w:type="gramEnd"/><w:r w:rsidR="003F0D09" w:rsidRPr="00373245"><w:rPr><w:b/><w:bCs/><w:noProof/><w:color w:val="000000" w:themeColor="text1"/><w:sz w:val="24"/><w:szCs w:val="24"/></w:rPr><w:t>_ffp_est_cost</w:t></w:r><w:r w:rsidR="003F0D09"><w:rPr><w:b/><w:bCs/><w:noProof/><w:color w:val="000000" w:themeColor="text1"/><w:sz w:val="24"/><w:szCs w:val="24"/></w:rPr><w:t>
    - </w:t></w:r><w:r w:rsidR="003F0D09" w:rsidRPr="003F0D09"><w:rPr><w:b/><w:bCs/><w:noProof/><w:color w:val="000000" w:themeColor="text1"/><w:sz w:val="24"/><w:szCs w:val="24"/></w:rPr><w:t xml:space="preserve"> </w:t></w:r><w:r w:rsidR="003F0D09" w:rsidRPr="00373245"><w:rPr><w:b/><w:bCs/><w:noProof/><w:color w:val="000000" w:themeColor="text1"/><w:sz w:val="24"/><w:szCs w:val="24"/></w:rPr><w:t>beazley</w:t></w:r><w:proofErr w:type="gramEnd"/><w:r w:rsidR="003F0D09" w:rsidRPr="00373245"><w:rPr><w:b/><w:bCs/><w:noProof/><w:color w:val="000000" w:themeColor="text1"/><w:sz w:val="24"/><w:szCs w:val="24"/></w:rPr><w:t>_total_costs</w:t></w:r><w:r w:rsidR="003F0D09"><w:rPr><w:b/><w:bCs/><w:noProof/><w:color w:val="000000" w:themeColor="text1"/><w:sz w:val="24"/><w:szCs w:val="24"/></w:rPr><w:t>
    - </w:t></w:r><w:r w:rsidR="003F0D09" w:rsidRPr="003F0D09"><w:rPr><w:noProof/><w:sz w:val="24"/><w:szCs w:val="24"/></w:rPr><w:t xml:space="preserve"> </w:t></w:r><w:r w:rsidR="003F0D09" w:rsidRPr="00373245"><w:rPr><w:noProof/><w:sz w:val="24"/><w:szCs w:val="24"/></w:rPr><w:t>beazley_ffp_endpoint_count</w:t></w:r><w:r w:rsidR="003F0D09"><w:rPr><w:noProof/><w:sz w:val="24"/><w:szCs w:val="24"/></w:rPr><w:t>
    - </w:t></w:r><w:r w:rsidR="003F0D09" w:rsidRPr="003F0D09"><w:rPr><w:rFonts w:ascii="Calibri" w:eastAsiaTheme="minorEastAsia" w:hAnsi="Calibri" w:cs="Calibri"/><w:noProof/><w:sz w:val="24"/><w:szCs w:val="24"/></w:rPr><w:t xml:space="preserve"> </w:t></w:r><w:r w:rsidR="003F0D09" w:rsidRPr="00373245"><w:rPr><w:rFonts w:ascii="Calibri" w:eastAsiaTheme="minorEastAsia" w:hAnsi="Calibri" w:cs="Calibri"/><w:noProof/><w:sz w:val="24"/><w:szCs w:val="24"/></w:rPr><w:t>formatted_date</w:t></w:r><w:r w:rsidR="003F0D09"><w:rPr><w:rFonts w:ascii="Calibri" w:eastAsiaTheme="minorEastAsia" w:hAnsi="Calibri" w:cs="Calibri"/><w:noProof/><w:sz w:val="24"/><w:szCs w:val="24"/></w:rPr><w:t>
  Matched (sample values):
    - client = <docxtpl.richtext.RichText object at 0x000002F430604D70>

Template: templates\base_templates\dfir\SOW__IR_template_Beazley_FFP_EDR_New_M365.docx
  Placeholders found: 9  Matched: 1  Missing: 8
  Missing:
    - </w:t></w:r><w:proofErr w:type="gramEnd"/><w:r w:rsidR="003F0D09" w:rsidRPr="003F0D09"><w:rPr><w:b/><w:bCs/><w:noProof/><w:color w:val="000000" w:themeColor="text1"/><w:sz w:val="24"/><w:szCs w:val="24"/></w:rPr><w:t xml:space="preserve"> </w:t></w:r><w:r w:rsidR="003F0D09" w:rsidRPr="00373245"><w:rPr><w:b/><w:bCs/><w:noProof/><w:color w:val="000000" w:themeColor="text1"/><w:sz w:val="24"/><w:szCs w:val="24"/></w:rPr><w:t>beazley_edr_fee</w:t></w:r><w:r w:rsidR="003F0D09"><w:rPr><w:b/><w:bCs/><w:noProof/><w:color w:val="000000" w:themeColor="text1"/><w:sz w:val="24"/><w:szCs w:val="24"/></w:rPr><w:t>
    - </w:t></w:r><w:proofErr w:type="gramEnd"/><w:r w:rsidR="003F0D09" w:rsidRPr="003F0D09"><w:rPr><w:b/><w:bCs/><w:noProof/><w:color w:val="000000" w:themeColor="text1"/><w:sz w:val="24"/><w:szCs w:val="24"/></w:rPr><w:t xml:space="preserve"> </w:t></w:r><w:r w:rsidR="003F0D09" w:rsidRPr="00373245"><w:rPr><w:b/><w:bCs/><w:noProof/><w:color w:val="000000" w:themeColor="text1"/><w:sz w:val="24"/><w:szCs w:val="24"/></w:rPr><w:t>beazley_ffp_est_cost</w:t></w:r><w:r w:rsidR="003F0D09"><w:rPr><w:b/><w:bCs/><w:noProof/><w:color w:val="000000" w:themeColor="text1"/><w:sz w:val="24"/><w:szCs w:val="24"/></w:rPr><w:t>
    - </w:t></w:r><w:proofErr w:type="gramEnd"/><w:r w:rsidR="003F0D09" w:rsidRPr="003F0D09"><w:rPr><w:b/><w:bCs/><w:noProof/><w:color w:val="000000" w:themeColor="text1"/><w:sz w:val="24"/><w:szCs w:val="24"/></w:rPr><w:t xml:space="preserve"> </w:t></w:r><w:r w:rsidR="003F0D09" w:rsidRPr="00373245"><w:rPr><w:b/><w:bCs/><w:noProof/><w:color w:val="000000" w:themeColor="text1"/><w:sz w:val="24"/><w:szCs w:val="24"/></w:rPr><w:t>beazley_total_costs</w:t></w:r><w:r w:rsidR="003F0D09"><w:rPr><w:b/><w:bCs/><w:noProof/><w:color w:val="000000" w:themeColor="text1"/><w:sz w:val="24"/><w:szCs w:val="24"/></w:rPr><w:t>
    - </w:t></w:r><w:proofErr w:type="spellStart"/><w:r w:rsidR="003F0D09"><w:rPr><w:rFonts w:ascii="Calibri" w:eastAsiaTheme="minorEastAsia" w:hAnsi="Calibri" w:cs="Calibri"/><w:sz w:val="24"/><w:szCs w:val="24"/><w:highlight w:val="green"/></w:rPr><w:t>lawfirm</w:t></w:r><w:proofErr w:type="spellEnd"/><w:r w:rsidR="003F0D09"><w:rPr><w:rFonts w:ascii="Calibri" w:eastAsiaTheme="minorEastAsia" w:hAnsi="Calibri" w:cs="Calibri"/><w:sz w:val="24"/><w:szCs w:val="24"/><w:highlight w:val="green"/></w:rPr><w:t>
    - </w:t></w:r><w:proofErr w:type="spellStart"/><w:r w:rsidR="003F7330"><w:rPr><w:sz w:val="24"/><w:szCs w:val="24"/></w:rPr><w:t>lawfirm</w:t></w:r><w:proofErr w:type="spellEnd"/><w:r w:rsidR="003F7330"><w:rPr><w:sz w:val="24"/><w:szCs w:val="24"/></w:rPr><w:t>
    - </w:t></w:r><w:proofErr w:type="spellStart"/><w:r><w:rPr><w:rFonts w:cstheme="minorHAnsi"/><w:b/><w:bCs/><w:sz w:val="24"/><w:szCs w:val="24"/></w:rPr><w:t>lawfirm</w:t></w:r><w:proofErr w:type="spellEnd"/><w:r><w:rPr><w:rFonts w:cstheme="minorHAnsi"/><w:b/><w:bCs/><w:sz w:val="24"/><w:szCs w:val="24"/></w:rPr><w:t>
    - </w:t></w:r><w:r w:rsidR="003F0D09" w:rsidRPr="003F0D09"><w:rPr><w:noProof/><w:sz w:val="24"/><w:szCs w:val="24"/></w:rPr><w:t xml:space="preserve"> </w:t></w:r><w:r w:rsidR="003F0D09" w:rsidRPr="00373245"><w:rPr><w:noProof/><w:sz w:val="24"/><w:szCs w:val="24"/></w:rPr><w:t>beazley_ffp_endpoint_count</w:t></w:r><w:r w:rsidR="003F0D09"><w:rPr><w:noProof/><w:sz w:val="24"/><w:szCs w:val="24"/></w:rPr><w:t>
    - </w:t></w:r><w:r w:rsidR="003F0D09" w:rsidRPr="003F0D09"><w:rPr><w:rFonts w:ascii="Calibri" w:eastAsiaTheme="minorEastAsia" w:hAnsi="Calibri" w:cs="Calibri"/><w:noProof/><w:sz w:val="24"/><w:szCs w:val="24"/></w:rPr><w:t xml:space="preserve"> </w:t></w:r><w:r w:rsidR="003F0D09" w:rsidRPr="00373245"><w:rPr><w:rFonts w:ascii="Calibri" w:eastAsiaTheme="minorEastAsia" w:hAnsi="Calibri" w:cs="Calibri"/><w:noProof/><w:sz w:val="24"/><w:szCs w:val="24"/></w:rPr><w:t>formatted_date</w:t></w:r><w:r w:rsidR="003F0D09"><w:rPr><w:rFonts w:ascii="Calibri" w:eastAsiaTheme="minorEastAsia" w:hAnsi="Calibri" w:cs="Calibri"/><w:noProof/><w:sz w:val="24"/><w:szCs w:val="24"/></w:rPr><w:t>
  Matched (sample values):
    - client = <docxtpl.richtext.RichText object at 0x000002F430604D70>

Template: templates\base_templates\dfir\SOW_chubb_IR_cs_codes_template.docx
  Placeholders found: 18  Matched: 11  Missing: 7
  Missing:
    - </w:t></w:r><w:r w:rsidR="00157E18" w:rsidRPr="00157E18"><w:rPr><w:rFonts w:cstheme="minorHAnsi"/><w:sz w:val="24"/><w:szCs w:val="24"/></w:rPr><w:t>chubb_endpoint_count</w:t></w:r><w:r w:rsidR="00157E18"><w:rPr><w:rFonts w:cstheme="minorHAnsi"/><w:sz w:val="24"/><w:szCs w:val="24"/></w:rPr><w:t xml:space="preserve">
    - chubb_endpoint_count
    - cs120_email_costs
    - cs120_forensic_costs
    - cs210_costs
    - cs410_costs
    - cs430_costs
  Matched (sample values):
    - client = <docxtpl.richtext.RichText object at 0x000002F430604D70>
    - cs120_email_hours = '0'
    - cs120_forensic_hours = '0'
    - cs210_hours = '0'
    - cs410_hours = '0'
    - edr_fee = '$5,000'
    - formatted_date = 'August 18, 2025'
    - lawfirm = <docxtpl.richtext.RichText object at 0x000002F4306ECCD0>
    - total_costs = '$5,000'
    - total_hours = '0'

Template: templates\base_templates\dpa\Template_Data_Processing_Agreement.docx
  Placeholders found: 2  Matched: 2  Missing: 0
  Matched (sample values):
    - client = <docxtpl.richtext.RichText object at 0x000002F430604D70>
    - formatted_date = 'August 18, 2025'

Template: templates\base_templates\msa_templates\Baker_&_Hostetler_LLP_(2-Party)_Master_Services_Agreement_Template.docx
  Placeholders found: 2  Matched: 2  Missing: 0
  Matched (sample values):
    - client = <docxtpl.richtext.RichText object at 0x000002F430604D70>
    - formatted_date = 'August 18, 2025'

Template: templates\base_templates\msa_templates\Baker_&_Hostetler_LLP_Beazley_TASB_Master_Services_Agreement_Template.docx
  Placeholders found: 2  Matched: 1  Missing: 1
  Missing:
    - </w:t></w:r><w:proofErr w:type="spellStart"/><w:r w:rsidRPr="009013BB"><w:rPr><w:rFonts w:asciiTheme="minorHAnsi" w:hAnsiTheme="minorHAnsi" w:cstheme="minorHAnsi"/><w:color w:val="111111"/><w:sz w:val="22"/><w:szCs w:val="22"/></w:rPr><w:t>formatted_date</w:t></w:r><w:proofErr w:type="spellEnd"/><w:r w:rsidRPr="009013BB"><w:rPr><w:rFonts w:asciiTheme="minorHAnsi" w:hAnsiTheme="minorHAnsi" w:cstheme="minorHAnsi"/><w:color w:val="111111"/><w:sz w:val="22"/><w:szCs w:val="22"/></w:rPr><w:t>
  Matched (sample values):
    - client = <docxtpl.richtext.RichText object at 0x000002F430604D70>

Template: templates\base_templates\msa_templates\Baker_Donelson_Bearman_Caldwell_&_Berkowitz_P.C._Master_Services_Agreement_Template.docx
  Placeholders found: 5  Matched: 4  Missing: 1
  Missing:
    - C</w:t></w:r><w:r w:rsidR="00C8019B"><w:rPr><w:rFonts w:asciiTheme="minorHAnsi" w:hAnsiTheme="minorHAnsi" w:cstheme="minorBidi"/><w:b/><w:bCs/><w:sz w:val="22"/><w:szCs w:val="22"/></w:rPr><w:t>lient</w:t></w:r><w:r><w:rPr><w:rFonts w:asciiTheme="minorHAnsi" w:hAnsiTheme="minorHAnsi" w:cstheme="minorBidi"/><w:b/><w:bCs/><w:sz w:val="22"/><w:szCs w:val="22"/></w:rPr><w:t>
  Matched (sample values):
    - client = <docxtpl.richtext.RichText object at 0x000002F430604D70>
    - client_address = <docxtpl.richtext.RichText object at 0x000002F4306ECB90>
    - formatted_date = 'August 18, 2025'
    - lawfirm = <docxtpl.richtext.RichText object at 0x000002F4306ECCD0>

Template: templates\base_templates\msa_templates\Cipriani_&_Werner_PC_Master_Services_Agreement_Template.docx
  Placeholders found: 3  Matched: 1  Missing: 2
  Missing:
    - </w:t></w:r><w:proofErr w:type="spellStart"/><w:r><w:rPr><w:rFonts w:asciiTheme="minorHAnsi" w:hAnsiTheme="minorHAnsi" w:cstheme="minorBidi"/><w:sz w:val="22"/><w:szCs w:val="22"/></w:rPr><w:t>client_address</w:t></w:r><w:proofErr w:type="spellEnd"/><w:r><w:rPr><w:rFonts w:asciiTheme="minorHAnsi" w:hAnsiTheme="minorHAnsi" w:cstheme="minorBidi"/><w:sz w:val="22"/><w:szCs w:val="22"/></w:rPr><w:t>
    - </w:t></w:r><w:proofErr w:type="spellStart"/><w:r><w:rPr><w:rFonts w:asciiTheme="minorHAnsi" w:hAnsiTheme="minorHAnsi" w:cstheme="minorBidi"/><w:sz w:val="22"/><w:szCs w:val="22"/></w:rPr><w:t>formatted_date</w:t></w:r><w:proofErr w:type="spellEnd"/><w:r><w:rPr><w:rFonts w:asciiTheme="minorHAnsi" w:hAnsiTheme="minorHAnsi" w:cstheme="minorBidi"/><w:sz w:val="22"/><w:szCs w:val="22"/></w:rPr><w:t>
  Matched (sample values):
    - client = <docxtpl.richtext.RichText object at 0x000002F430604D70>

Template: templates\base_templates\msa_templates\Clark_Hill_PLC_Master_Services_Agreement.docx
  Placeholders found: 3  Matched: 2  Missing: 1
  Missing:
    - </w:t></w:r><w:r w:rsidRPr="00BC79EA"><w:rPr><w:rFonts w:ascii="Garamond" w:hAnsi="Garamond" w:cs="Arial"/><w:color w:val="111111"/></w:rPr><w:t>client</w:t></w:r><w:r w:rsidR="009519AA"><w:rPr><w:rFonts w:ascii="Garamond" w:hAnsi="Garamond" w:cs="Arial"/><w:color w:val="111111"/></w:rPr><w:t>
  Matched (sample values):
    - client = <docxtpl.richtext.RichText object at 0x000002F430604D70>
    - date = 'August 18, 2025'

Template: templates\base_templates\msa_templates\Constangy_Brooks_Smith_&_Prophete_LLP_Master_Services_Agreement_Template.docx
  Placeholders found: 3  Matched: 3  Missing: 0
  Matched (sample values):
    - client = <docxtpl.richtext.RichText object at 0x000002F430604D70>
    - client_address = <docxtpl.richtext.RichText object at 0x000002F4306ECB90>
    - formatted_date = 'August 18, 2025'

Template: templates\base_templates\msa_templates\Dykema_Gossett_PLLC_Master_Service_Agreement_Template.docx
  Placeholders found: 3  Matched: 1  Missing: 2
  Missing:
    - </w:t></w:r><w:proofErr w:type="spellStart"/><w:r><w:rPr><w:rFonts w:asciiTheme="minorHAnsi" w:hAnsiTheme="minorHAnsi" w:cstheme="minorBidi"/><w:sz w:val="22"/><w:szCs w:val="22"/></w:rPr><w:t>client_address</w:t></w:r><w:proofErr w:type="spellEnd"/><w:r><w:rPr><w:rFonts w:asciiTheme="minorHAnsi" w:hAnsiTheme="minorHAnsi" w:cstheme="minorBidi"/><w:sz w:val="22"/><w:szCs w:val="22"/></w:rPr><w:t>
    - </w:t></w:r><w:proofErr w:type="spellStart"/><w:r><w:rPr><w:rFonts w:asciiTheme="minorHAnsi" w:hAnsiTheme="minorHAnsi" w:cstheme="minorBidi"/><w:sz w:val="22"/><w:szCs w:val="22"/></w:rPr><w:t>formatted_date</w:t></w:r><w:proofErr w:type="spellEnd"/><w:r><w:rPr><w:rFonts w:asciiTheme="minorHAnsi" w:hAnsiTheme="minorHAnsi" w:cstheme="minorBidi"/><w:sz w:val="22"/><w:szCs w:val="22"/></w:rPr><w:t>
  Matched (sample values):
    - client = <docxtpl.richtext.RichText object at 0x000002F430604D70>

Template: templates\base_templates\msa_templates\Eckert_Seamans_Cherin_&_Mellott_LLC_Master_Service_Agreement_Template.docx
  Placeholders found: 3  Matched: 2  Missing: 1
  Missing:
    - </w:t></w:r><w:proofErr w:type="spellStart"/><w:r><w:rPr><w:rFonts w:asciiTheme="minorHAnsi" w:hAnsiTheme="minorHAnsi" w:cstheme="minorBidi"/><w:sz w:val="22"/><w:szCs w:val="22"/></w:rPr><w:t>client_address</w:t></w:r><w:proofErr w:type="spellEnd"/><w:r><w:rPr><w:rFonts w:asciiTheme="minorHAnsi" w:hAnsiTheme="minorHAnsi" w:cstheme="minorBidi"/><w:sz w:val="22"/><w:szCs w:val="22"/></w:rPr><w:t>
  Matched (sample values):
    - client = <docxtpl.richtext.RichText object at 0x000002F430604D70>
    - date = 'August 18, 2025'

Template: templates\base_templates\msa_templates\Gordon_Rees_Scully_Mansukhani_LLP_Master_Services_Agreement_Template.docx
  Placeholders found: 3  Matched: 2  Missing: 1
  Missing:
    - </w:t></w:r><w:proofErr w:type="spellStart"/><w:r w:rsidRPr="009724BF"><w:rPr><w:rFonts w:asciiTheme="minorHAnsi" w:hAnsiTheme="minorHAnsi" w:cstheme="minorHAnsi"/><w:color w:val="111111"/><w:sz w:val="22"/><w:szCs w:val="22"/></w:rPr><w:t>client_address</w:t></w:r><w:proofErr w:type="spellEnd"/><w:r w:rsidRPr="009724BF"><w:rPr><w:rFonts w:asciiTheme="minorHAnsi" w:hAnsiTheme="minorHAnsi" w:cstheme="minorHAnsi"/><w:color w:val="111111"/><w:sz w:val="22"/><w:szCs w:val="22"/></w:rPr><w:t>
  Matched (sample values):
    - client = <docxtpl.richtext.RichText object at 0x000002F430604D70>
    - date = 'August 18, 2025'

Template: templates\base_templates\msa_templates\Greenberg_Traurig_LLP_Master_Services_Agreement_Template.docx
  Placeholders found: 2  Matched: 1  Missing: 1
  Missing:
    - </w:t></w:r><w:proofErr w:type="spellStart"/><w:r w:rsidR="00384BAF" w:rsidRPr="0000641A"><w:rPr><w:b/><w:bCs/></w:rPr><w:t>legal_date</w:t></w:r><w:proofErr w:type="spellEnd"/><w:r w:rsidR="00384BAF" w:rsidRPr="0000641A"><w:rPr><w:b/><w:bCs/></w:rPr><w:t>
  Matched (sample values):
    - client = <docxtpl.richtext.RichText object at 0x000002F430604D70>

Template: templates\base_templates\msa_templates\IR_Services_2_Party_Master_Services_Agreement_Template.docx
  Placeholders found: 3  Matched: 3  Missing: 0
  Matched (sample values):
    - client = <docxtpl.richtext.RichText object at 0x000002F430604D70>
    - client_address = <docxtpl.richtext.RichText object at 0x000002F4306ECB90>
    - formatted_date = 'August 18, 2025'

Template: templates\base_templates\msa_templates\IR_Services_3_Party_Master_Services_Agreement_Template.docx
  Placeholders found: 4  Matched: 4  Missing: 0
  Matched (sample values):
    - client = <docxtpl.richtext.RichText object at 0x000002F430604D70>
    - client_address = <docxtpl.richtext.RichText object at 0x000002F4306ECB90>
    - formatted_date = 'August 18, 2025'
    - lawfirm = <docxtpl.richtext.RichText object at 0x000002F4306ECCD0>

Template: templates\base_templates\msa_templates\Jackson_Lewis_PC_Master_Services_Agreement_Template.docx
  Placeholders found: 2  Matched: 2  Missing: 0
  Matched (sample values):
    - client = <docxtpl.richtext.RichText object at 0x000002F430604D70>
    - formatted_date = 'August 18, 2025'

Template: templates\base_templates\msa_templates\Jackson_Lewis_PC_with_BAA_Master_Services_Agreement_Template.docx
  Placeholders found: 2  Matched: 2  Missing: 0
  Matched (sample values):
    - client = <docxtpl.richtext.RichText object at 0x000002F430604D70>
    - date = 'August 18, 2025'

Template: templates\base_templates\msa_templates\Lewis_Brisbois_Bisgaard_&_Smith_LLP_Master_Services_Agreement_Template.docx
  Placeholders found: 4  Matched: 1  Missing: 3
  Missing:
    - </w:t></w:r><w:proofErr w:type="spellStart"/><w:r><w:rPr><w:rFonts w:asciiTheme="minorHAnsi" w:hAnsiTheme="minorHAnsi" w:cstheme="minorBidi"/><w:sz w:val="22"/><w:szCs w:val="22"/></w:rPr><w:t>formatted_date</w:t></w:r><w:proofErr w:type="spellEnd"/><w:r><w:rPr><w:rFonts w:asciiTheme="minorHAnsi" w:hAnsiTheme="minorHAnsi" w:cstheme="minorBidi"/><w:sz w:val="22"/><w:szCs w:val="22"/></w:rPr><w:t>
    - </w:t></w:r><w:r w:rsidR="00C172E1"><w:rPr><w:rFonts w:asciiTheme="minorHAnsi" w:hAnsiTheme="minorHAnsi" w:cstheme="minorBidi"/><w:b/><w:bCs/><w:sz w:val="22"/><w:szCs w:val="22"/></w:rPr><w:t>Client</w:t></w:r><w:r w:rsidRPr="000D4C99"><w:rPr><w:rFonts w:asciiTheme="minorHAnsi" w:hAnsiTheme="minorHAnsi" w:cstheme="minorBidi"/><w:b/><w:bCs/><w:sz w:val="22"/><w:szCs w:val="22"/></w:rPr><w:t>
    - </w:t></w:r><w:r w:rsidR="00C172E1"><w:rPr><w:rFonts w:asciiTheme="minorHAnsi" w:hAnsiTheme="minorHAnsi" w:cstheme="minorBidi"/><w:sz w:val="22"/><w:szCs w:val="22"/></w:rPr><w:t>CLIENT</w:t></w:r><w:r><w:rPr><w:rFonts w:asciiTheme="minorHAnsi" w:hAnsiTheme="minorHAnsi" w:cstheme="minorBidi"/><w:sz w:val="22"/><w:szCs w:val="22"/></w:rPr><w:t>
  Matched (sample values):
    - client_address = <docxtpl.richtext.RichText object at 0x000002F4306ECB90>

Template: templates\base_templates\msa_templates\Locke_Lord_Master_Services_Agreement_Template.docx
  Placeholders found: 4  Matched: 3  Missing: 1
  Missing:
    - </w:t></w:r><w:r w:rsidR="00096D19"><w:rPr><w:rFonts w:asciiTheme="minorHAnsi" w:hAnsiTheme="minorHAnsi" w:cstheme="minorBidi"/><w:b/><w:bCs/><w:sz w:val="22"/><w:szCs w:val="22"/></w:rPr><w:t>Client</w:t></w:r><w:r w:rsidRPr="000275FC"><w:rPr><w:rFonts w:asciiTheme="minorHAnsi" w:hAnsiTheme="minorHAnsi" w:cstheme="minorBidi"/><w:b/><w:bCs/><w:sz w:val="22"/><w:szCs w:val="22"/></w:rPr><w:t>
  Matched (sample values):
    - client = <docxtpl.richtext.RichText object at 0x000002F430604D70>
    - client_address = <docxtpl.richtext.RichText object at 0x000002F4306ECB90>
    - formatted_date = 'August 18, 2025'

Template: templates\base_templates\msa_templates\Maynard_Nexsen_PC_Master_Services_Agreement_Template.docx
  Placeholders found: 4  Matched: 2  Missing: 2
  Missing:
    - </w:t></w:r><w:proofErr w:type="spellStart"/><w:r w:rsidR="00CD05B1"><w:rPr><w:rFonts w:asciiTheme="minorHAnsi" w:hAnsiTheme="minorHAnsi" w:cstheme="minorBidi"/><w:sz w:val="22"/><w:szCs w:val="22"/></w:rPr><w:t>formatted_date</w:t></w:r><w:proofErr w:type="spellEnd"/><w:r><w:rPr><w:rFonts w:asciiTheme="minorHAnsi" w:hAnsiTheme="minorHAnsi" w:cstheme="minorBidi"/><w:sz w:val="22"/><w:szCs w:val="22"/></w:rPr><w:t>
    - </w:t></w:r><w:proofErr w:type="spellStart"/><w:r><w:rPr><w:rFonts w:asciiTheme="minorHAnsi" w:hAnsiTheme="minorHAnsi" w:cstheme="minorBidi"/><w:sz w:val="22"/><w:szCs w:val="22"/></w:rPr><w:t>client_address</w:t></w:r><w:proofErr w:type="spellEnd"/><w:r><w:rPr><w:rFonts w:asciiTheme="minorHAnsi" w:hAnsiTheme="minorHAnsi" w:cstheme="minorBidi"/><w:sz w:val="22"/><w:szCs w:val="22"/></w:rPr><w:t xml:space="preserve">
  Matched (sample values):
    - Client = <docxtpl.richtext.RichText object at 0x000002F430604D70>
    - client = <docxtpl.richtext.RichText object at 0x000002F430604D70>

Template: templates\base_templates\msa_templates\McDonald_Hopkins_LLC_Master_Services_Agreement_Template.docx
  Placeholders found: 4  Matched: 2  Missing: 2
  Missing:
    - </w:t></w:r><w:proofErr w:type="spellStart"/><w:r><w:rPr><w:rFonts w:asciiTheme="minorHAnsi" w:hAnsiTheme="minorHAnsi" w:cstheme="minorBidi"/><w:sz w:val="22"/><w:szCs w:val="22"/></w:rPr><w:t>client_address</w:t></w:r><w:proofErr w:type="spellEnd"/><w:r><w:rPr><w:rFonts w:asciiTheme="minorHAnsi" w:hAnsiTheme="minorHAnsi" w:cstheme="minorBidi"/><w:sz w:val="22"/><w:szCs w:val="22"/></w:rPr><w:t>
    - </w:t></w:r><w:proofErr w:type="spellStart"/><w:r><w:rPr><w:rFonts w:asciiTheme="minorHAnsi" w:hAnsiTheme="minorHAnsi" w:cstheme="minorBidi"/><w:sz w:val="22"/><w:szCs w:val="22"/></w:rPr><w:t>formatted_date</w:t></w:r><w:proofErr w:type="spellEnd"/><w:r><w:rPr><w:rFonts w:asciiTheme="minorHAnsi" w:hAnsiTheme="minorHAnsi" w:cstheme="minorBidi"/><w:sz w:val="22"/><w:szCs w:val="22"/></w:rPr><w:t>
  Matched (sample values):
    - CLIENT = <docxtpl.richtext.RichText object at 0x000002F430604D70>
    - Client = <docxtpl.richtext.RichText object at 0x000002F430604D70>

Template: templates\base_templates\msa_templates\Mullen_Coughlin_LLC_Master_Services_Agreement_Template.docx
  Placeholders found: 2  Matched: 2  Missing: 0
  Matched (sample values):
    - client = <docxtpl.richtext.RichText object at 0x000002F430604D70>
    - formatted_date = 'August 18, 2025'

Template: templates\base_templates\msa_templates\Nelson_Mullins_Riley_&_Scarborough_LLP_Master_Services_Agreement_Template.docx
  Placeholders found: 2  Matched: 2  Missing: 0
  Matched (sample values):
    - client = <docxtpl.richtext.RichText object at 0x000002F430604D70>
    - formatted_date = 'August 18, 2025'

Template: templates\base_templates\msa_templates\Norton_Rose_Fulbright_Master_Services_Agreement_Template.docx
  Placeholders found: 2  Matched: 2  Missing: 0
  Matched (sample values):
    - client = <docxtpl.richtext.RichText object at 0x000002F430604D70>
    - formatted_date = 'August 18, 2025'

Template: templates\base_templates\msa_templates\Octillo_Law_PLLC_Master_Services_Agreement_Template.docx
  Placeholders found: 3  Matched: 3  Missing: 0
  Matched (sample values):
    - client = <docxtpl.richtext.RichText object at 0x000002F430604D70>
    - client_address = <docxtpl.richtext.RichText object at 0x000002F4306ECB90>
    - formatted_date = 'August 18, 2025'

Template: templates\base_templates\msa_templates\Ogletree_Deakins_Master_Services_Agreement_Template.docx
  Placeholders found: 4  Matched: 2  Missing: 2
  Missing:
    - </w:t></w:r><w:proofErr w:type="spellStart"/><w:r w:rsidRPr="54BA3699"><w:rPr><w:rFonts w:asciiTheme="minorHAnsi" w:hAnsiTheme="minorHAnsi" w:cstheme="minorBidi"/><w:sz w:val="22"/><w:szCs w:val="22"/></w:rPr><w:t>client_address</w:t></w:r><w:proofErr w:type="spellEnd"/><w:r w:rsidRPr="54BA3699"><w:rPr><w:rFonts w:asciiTheme="minorHAnsi" w:hAnsiTheme="minorHAnsi" w:cstheme="minorBidi"/><w:sz w:val="22"/><w:szCs w:val="22"/></w:rPr><w:t>
    - </w:t></w:r><w:proofErr w:type="spellStart"/><w:r w:rsidRPr="54BA3699"><w:rPr><w:rFonts w:asciiTheme="minorHAnsi" w:hAnsiTheme="minorHAnsi" w:cstheme="minorBidi"/><w:sz w:val="22"/><w:szCs w:val="22"/></w:rPr><w:t>formatted_date</w:t></w:r><w:proofErr w:type="spellEnd"/><w:r w:rsidRPr="54BA3699"><w:rPr><w:rFonts w:asciiTheme="minorHAnsi" w:hAnsiTheme="minorHAnsi" w:cstheme="minorBidi"/><w:sz w:val="22"/><w:szCs w:val="22"/></w:rPr><w:t>
  Matched (sample values):
    - Client = <docxtpl.richtext.RichText object at 0x000002F430604D70>
    - client = <docxtpl.richtext.RichText object at 0x000002F430604D70>

Template: templates\base_templates\msa_templates\Ruskin_Moscou_Faltischek_P.C._Master_Services_Agreement_Template.docx
  Placeholders found: 2  Matched: 2  Missing: 0
  Matched (sample values):
    - client = <docxtpl.richtext.RichText object at 0x000002F430604D70>
    - formatted_date = 'August 18, 2025'

Template: templates\base_templates\msa_templates\Shook_Hardy_&_Bacon_LLP_Master_Services_Agreement_Template.docx
  Placeholders found: 4  Matched: 2  Missing: 2
  Missing:
    - </w:t></w:r><w:proofErr w:type="spellStart"/><w:r><w:rPr><w:rFonts w:asciiTheme="minorHAnsi" w:hAnsiTheme="minorHAnsi" w:cstheme="minorBidi"/><w:sz w:val="22"/><w:szCs w:val="22"/></w:rPr><w:t>client_a</w:t></w:r><w:r w:rsidR="00A9002C"><w:rPr><w:rFonts w:asciiTheme="minorHAnsi" w:hAnsiTheme="minorHAnsi" w:cstheme="minorBidi"/><w:sz w:val="22"/><w:szCs w:val="22"/></w:rPr><w:t>d</w:t></w:r><w:r><w:rPr><w:rFonts w:asciiTheme="minorHAnsi" w:hAnsiTheme="minorHAnsi" w:cstheme="minorBidi"/><w:sz w:val="22"/><w:szCs w:val="22"/></w:rPr><w:t>dress</w:t></w:r><w:proofErr w:type="spellEnd"/><w:r><w:rPr><w:rFonts w:asciiTheme="minorHAnsi" w:hAnsiTheme="minorHAnsi" w:cstheme="minorBidi"/><w:sz w:val="22"/><w:szCs w:val="22"/></w:rPr><w:t xml:space="preserve">
    - </w:t></w:r><w:proofErr w:type="spellStart"/><w:r><w:rPr><w:rFonts w:asciiTheme="minorHAnsi" w:hAnsiTheme="minorHAnsi" w:cstheme="minorBidi"/><w:sz w:val="22"/><w:szCs w:val="22"/></w:rPr><w:t>formatted_date</w:t></w:r><w:proofErr w:type="spellEnd"/><w:r><w:rPr><w:rFonts w:asciiTheme="minorHAnsi" w:hAnsiTheme="minorHAnsi" w:cstheme="minorBidi"/><w:sz w:val="22"/><w:szCs w:val="22"/></w:rPr><w:t>
  Matched (sample values):
    - Client = <docxtpl.richtext.RichText object at 0x000002F430604D70>
    - client = <docxtpl.richtext.RichText object at 0x000002F430604D70>

Template: templates\base_templates\msa_templates\The_Beckage_Firm_PLLC_Master_Services_Agreement_Template.docx
  Placeholders found: 3  Matched: 1  Missing: 2
  Missing:
    - </w:t></w:r><w:proofErr w:type="spellStart"/><w:r w:rsidR="00842A29" w:rsidRPr="00842A29"><w:rPr><w:rFonts w:asciiTheme="minorHAnsi" w:hAnsiTheme="minorHAnsi" w:cstheme="minorBidi"/><w:b/><w:bCs/><w:sz w:val="22"/><w:szCs w:val="22"/></w:rPr><w:t>formatted_date</w:t></w:r><w:proofErr w:type="spellEnd"/><w:r w:rsidR="00842A29" w:rsidRPr="00842A29"><w:rPr><w:rFonts w:asciiTheme="minorHAnsi" w:hAnsiTheme="minorHAnsi" w:cstheme="minorBidi"/><w:b/><w:bCs/><w:sz w:val="22"/><w:szCs w:val="22"/></w:rPr><w:t>
    - </w:t></w:r><w:proofErr w:type="spellStart"/><w:r w:rsidR="00842A29" w:rsidRPr="00842A29"><w:rPr><w:rFonts w:asciiTheme="minorHAnsi" w:hAnsiTheme="minorHAnsi" w:cstheme="minorBidi"/><w:sz w:val="22"/><w:szCs w:val="22"/></w:rPr><w:t>client_address</w:t></w:r><w:proofErr w:type="spellEnd"/><w:r w:rsidR="00842A29" w:rsidRPr="00842A29"><w:rPr><w:rFonts w:asciiTheme="minorHAnsi" w:hAnsiTheme="minorHAnsi" w:cstheme="minorBidi"/><w:sz w:val="22"/><w:szCs w:val="22"/></w:rPr><w:t>
  Matched (sample values):
    - client = <docxtpl.richtext.RichText object at 0x000002F430604D70>

Template: templates\base_templates\msa_templates\Woods_Rogers_Master_Services_Agreement_Template.docx
  Placeholders found: 1  Matched: 1  Missing: 0
  Matched (sample values):
    - formatted_date = 'August 18, 2025'

Template: templates\base_templates\rr\SOW_ON_Template_Beazley_Only_Recovery_&_Restoration_Support.docx
  Placeholders found: 6  Matched: 0  Missing: 6
  Missing:
    - beazley_rr_hours
    - beazley_rr_hours</w:t></w:r><w:proofErr w:type="gramStart"/><w:r w:rsidRPr="00FF0118"><w:rPr><w:sz w:val="24"/><w:szCs w:val="24"/></w:rPr><w:t>
    - beazley_rr_total_cost
    - client
    - formatted_date
    - lawfirm

Template: templates\base_templates\rr\SOW_ON_Template_Recovery_&_Restoration_Support.docx
  Placeholders found: 5  Matched: 0  Missing: 5
  Missing:
    - Client
    - Lawfirm
    - formatted_date
    - rr_total_cost
    - rr_total_hours

Template: templates\base_templates\taci\SOW_Coalition_Ransom_Consulting_FFP_template.docx
  Placeholders found: 6  Matched: 2  Missing: 4
  Missing:
    - </w:t></w:r><w:proofErr w:type="spellStart"/><w:r w:rsidRPr="003B3A99"><w:rPr><w:rStyle w:val="normaltextrun"/><w:rFonts w:cstheme="minorHAnsi"/><w:color w:val="000000"/><w:sz w:val="24"/><w:szCs w:val="24"/><w:shd w:val="clear" w:color="auto" w:fill="FFFFFF"/></w:rPr><w:t>lawfirm</w:t></w:r><w:proofErr w:type="spellEnd"/><w:r w:rsidRPr="003B3A99"><w:rPr><w:rStyle w:val="normaltextrun"/><w:rFonts w:cstheme="minorHAnsi"/><w:color w:val="000000"/><w:sz w:val="24"/><w:szCs w:val="24"/><w:shd w:val="clear" w:color="auto" w:fill="FFFFFF"/></w:rPr><w:t xml:space="preserve">
    - </w:t></w:r><w:proofErr w:type="spellStart"/><w:r w:rsidRPr="00FF15D1"><w:rPr><w:rFonts w:ascii="Calibri" w:eastAsiaTheme="minorEastAsia" w:hAnsi="Calibri" w:cs="Calibri"/><w:sz w:val="24"/><w:szCs w:val="24"/></w:rPr><w:t>formatted_date</w:t></w:r><w:proofErr w:type="spellEnd"/><w:r w:rsidRPr="00FF15D1"><w:rPr><w:rFonts w:ascii="Calibri" w:eastAsiaTheme="minorEastAsia" w:hAnsi="Calibri" w:cs="Calibri"/><w:sz w:val="24"/><w:szCs w:val="24"/></w:rPr><w:t>
    - </w:t></w:r><w:proofErr w:type="spellStart"/><w:r w:rsidRPr="00FF15D1"><w:rPr><w:rFonts w:ascii="Calibri" w:eastAsiaTheme="minorEastAsia" w:hAnsi="Calibri" w:cs="Calibri"/><w:sz w:val="24"/><w:szCs w:val="24"/></w:rPr><w:t>lawfirm</w:t></w:r><w:proofErr w:type="spellEnd"/><w:r w:rsidRPr="00FF15D1"><w:rPr><w:rFonts w:ascii="Calibri" w:eastAsiaTheme="minorEastAsia" w:hAnsi="Calibri" w:cs="Calibri"/><w:sz w:val="24"/><w:szCs w:val="24"/></w:rPr><w:t xml:space="preserve">
    - </w:t></w:r><w:proofErr w:type="spellStart"/><w:r><w:rPr><w:rFonts w:cstheme="minorHAnsi"/><w:b/><w:bCs/><w:sz w:val="24"/><w:szCs w:val="24"/></w:rPr><w:t>lawfirm</w:t></w:r><w:proofErr w:type="spellEnd"/><w:r><w:rPr><w:rFonts w:cstheme="minorHAnsi"/><w:b/><w:bCs/><w:sz w:val="24"/><w:szCs w:val="24"/></w:rPr><w:t>
  Matched (sample values):
    - Client = <docxtpl.richtext.RichText object at 0x000002F430604D70>
    - client = <docxtpl.richtext.RichText object at 0x000002F430604D70>

Template: templates\base_templates\taci\SOW_RN-Template_Ransom_Site_Monitoring.docx
  Placeholders found: 9  Matched: 9  Missing: 0
  Matched (sample values):
    - client = <docxtpl.richtext.RichText object at 0x000002F430604D70>
    - client_address = <docxtpl.richtext.RichText object at 0x000002F4306ECB90>
    - formatted_date = 'August 18, 2025'
    - law_firm = <docxtpl.richtext.RichText object at 0x000002F4306ECCD0>
    - lawfirm = <docxtpl.richtext.RichText object at 0x000002F4306ECCD0>
    - taci_phase1_costs = '$12,800'
    - taci_phase2_costs = '$8,000'
    - taci_rate = '$320'
    - taci_total_cost = '$20,800'

Template: templates\base_templates\taci\SOW_RN_Template_Beckage_Firm_Ransom_Consulting.docx
  Placeholders found: 2  Matched: 2  Missing: 0
  Matched (sample values):
    - Client = <docxtpl.richtext.RichText object at 0x000002F430604D70>
    - beckage_taci_phase1_costs = '$12,800'

Template: templates\base_templates\taci\SOW_RN_Template_Chubb_Only_Ransom_Consulting.docx
  Placeholders found: 3  Matched: 3  Missing: 0
  Matched (sample values):
    - Client = <docxtpl.richtext.RichText object at 0x000002F430604D70>
    - Lawfirm = <docxtpl.richtext.RichText object at 0x000002F4306ECCD0>
    - formatted_date = 'August 18, 2025'

Template: templates\base_templates\taci\SOW_RN_Template_Client_Data_Download.docx
  Placeholders found: 9  Matched: 9  Missing: 0
  Matched (sample values):
    - client = <docxtpl.richtext.RichText object at 0x000002F430604D70>
    - client_address = <docxtpl.richtext.RichText object at 0x000002F4306ECB90>
    - formatted_date = 'August 18, 2025'
    - law_firm = <docxtpl.richtext.RichText object at 0x000002F4306ECCD0>
    - lawfirm = <docxtpl.richtext.RichText object at 0x000002F4306ECCD0>
    - taci_phase1_costs = '$12,800'
    - taci_phase2_costs = '$8,000'
    - taci_rate = '$320'
    - taci_total_cost = '$20,800'

Template: templates\base_templates\taci\SOW_Ransom_Consulting_template.docx
  Placeholders found: 7  Matched: 7  Missing: 0
  Matched (sample values):
    - client = <docxtpl.richtext.RichText object at 0x000002F430604D70>
    - formatted_date = 'August 18, 2025'
    - lawfirm = <docxtpl.richtext.RichText object at 0x000002F4306ECCD0>
    - taci_phase1_costs = '$12,800'
    - taci_phase2_costs = '$8,000'
    - taci_rate = '$320'
    - taci_total_cost = '$20,800'

Summary: 60 templates scanned, 571 placeholders total, 219 missing across all.