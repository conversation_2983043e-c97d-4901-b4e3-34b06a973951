"""
Cross-platform utility functions for PACE application.
Handles platform-specific operations like opening directories.
"""

import os
import sys
import platform
import subprocess


def open_directory(directory_path):
    """
    Open a directory in the system's default file manager.
    Works cross-platform (Windows, macOS, Linux).
    
    Args:
        directory_path (str): Path to the directory to open
        
    Returns:
        bool: True if successful, False otherwise
    """
    if not os.path.exists(directory_path):
        print(f"WARNING: Directory {directory_path} does not exist")
        return False
    
    try:
        system = platform.system()
        
        if system == "Windows":
            # Windows: Use explorer
            subprocess.Popen(['explorer', directory_path])
            print(f"DEBUG - Opened directory using Windows Explorer: {directory_path}")
            
        elif system == "Darwin":  # macOS
            # macOS: Use open command to open in Finder
            subprocess.call(['open', directory_path])
            print(f"DEBUG - Opened directory using macOS Finder: {directory_path}")
            
        elif system == "Linux":
            # Linux: Try common file managers
            file_managers = ['xdg-open', 'nautilus', 'dolphin', 'thunar', 'pcmanfm']
            
            for fm in file_managers:
                try:
                    subprocess.Popen([fm, directory_path])
                    print(f"DEBUG - Opened directory using {fm}: {directory_path}")
                    break
                except FileNotFoundError:
                    continue
            else:
                print(f"WARNING: No suitable file manager found on Linux")
                return False
                
        else:
            print(f"WARNING: Unsupported platform: {system}")
            return False
            
        return True
        
    except Exception as e:
        print(f"ERROR: Failed to open directory {directory_path}: {e}")
        return False


def get_platform_info():
    """
    Get detailed platform information.
    
    Returns:
        dict: Platform information including system, version, architecture
    """
    return {
        'system': platform.system(),
        'release': platform.release(),
        'version': platform.version(),
        'machine': platform.machine(),
        'processor': platform.processor(),
        'python_version': sys.version,
        'is_windows': platform.system() == "Windows",
        'is_macos': platform.system() == "Darwin",
        'is_linux': platform.system() == "Linux"
    }


def get_executable_extension():
    """
    Get the appropriate executable extension for the current platform.
    
    Returns:
        str: File extension ('.exe' for Windows, '' for Unix-like systems)
    """
    return '.exe' if platform.system() == "Windows" else ''


def get_icon_extension():
    """
    Get the appropriate icon file extension for the current platform.
    
    Returns:
        str: Icon file extension ('.ico' for Windows, '.icns' for macOS, '.png' for Linux)
    """
    system = platform.system()
    if system == "Windows":
        return '.ico'
    elif system == "Darwin":
        return '.icns'
    else:
        return '.png'


def is_bundled_app():
    """
    Check if the application is running as a bundled executable (PyInstaller).
    
    Returns:
        bool: True if running as bundled app, False if running as script
    """
    return getattr(sys, 'frozen', False) and hasattr(sys, '_MEIPASS')


def get_resource_path(relative_path):
    """
    Get the absolute path to a resource file.
    Works both in development and when bundled with PyInstaller.
    
    Args:
        relative_path (str): Relative path to the resource
        
    Returns:
        str: Absolute path to the resource
    """
    if is_bundled_app():
        # Running as bundled executable
        base_path = sys._MEIPASS
    else:
        # Running as script
        base_path = os.path.dirname(os.path.abspath(__file__))
        # Go up one level since this is in utils/ subdirectory
        base_path = os.path.dirname(base_path)
    
    return os.path.join(base_path, relative_path)


def show_platform_debug_info():
    """
    Print detailed platform and environment information for debugging.
    """
    info = get_platform_info()
    
    print("=" * 50)
    print("PLATFORM DEBUG INFORMATION")
    print("=" * 50)
    print(f"System: {info['system']}")
    print(f"Release: {info['release']}")
    print(f"Machine: {info['machine']}")
    print(f"Processor: {info['processor']}")
    print(f"Python Version: {info['python_version']}")
    print(f"Bundled App: {is_bundled_app()}")
    print(f"Script Directory: {os.path.dirname(os.path.abspath(__file__))}")
    print(f"Current Working Directory: {os.getcwd()}")
    
    if is_bundled_app():
        print(f"PyInstaller Temp Directory: {sys._MEIPASS}")
    
    print("=" * 50)


if __name__ == "__main__":
    # Test the utility functions
    show_platform_debug_info()
    
    # Test directory opening with current directory
    print("\nTesting directory opening with current directory...")
    success = open_directory(os.getcwd())
    print(f"Directory opening {'succeeded' if success else 'failed'}")
