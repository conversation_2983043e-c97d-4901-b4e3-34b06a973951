"""
Utility module for replacing placeholders in Word documents using python-docx.
This is an alternative to docxtpl that might work better for some templates.
"""

import os
import re
import logging
import html
from docx import Document
from utils.path_resolver import resolve_template_path, get_all_template_files

logger = logging.getLogger(__name__)

def replace_placeholders_in_docx(template_path, output_path, placeholders):
    """
    Replace placeholders in a Word document using python-docx.

    Args:
        template_path (str): Path to the template file
        output_path (str): Path to save the generated document
        placeholders (dict): Dictionary of placeholders and values

    Returns:
        str: Path to the generated document, or None if generation failed
    """
    try:
        # Resolve the template path for bundled environments
        resolved_template_path = resolve_template_path(template_path)
        logger.info(f"Resolved template path: {resolved_template_path}")

        # Check if the template exists
        if not os.path.exists(resolved_template_path):
            logger.error(f"Template not found at resolved path: {resolved_template_path}")

            # List all available templates for debugging
            logger.info("Available templates:")
            for template_file in get_all_template_files():
                logger.info(f"  - {template_file}")

            return None

        # Create the output directory if it doesn't exist
        os.makedirs(os.path.dirname(output_path), exist_ok=True)

        # Load the document
        logger.info(f"Loading document: {resolved_template_path}")
        doc = Document(resolved_template_path)

        # Process placeholders safely: never inject raw XML strings
        processed_placeholders = {}
        for key, value in placeholders.items():
            if value is None:
                processed_value = ""
            elif hasattr(value, 'text'):
                # Extract plain text from RichText-like objects
                try:
                    processed_value = str(value.text)
                except Exception:
                    processed_value = str(value)
            elif hasattr(value, 'xml'):
                # Strip tags from XML-rich values
                processed_value = re.sub(r'<[^>]+>', '', str(value))
            elif not isinstance(value, str):
                processed_value = str(value)
            else:
                processed_value = value

            # Clean XML/HTML artifacts for all string values
            if isinstance(processed_value, str):
                processed_value = re.sub(r'<[^>]+>', '', processed_value)
                processed_value = processed_value.replace('&amp;', '&')
                processed_value = html.unescape(processed_value)
                entity_map = {
                    '&#x27;': "'",  # Apostrophe
                    '&apos;': "'",  # Apostrophe
                    '&quot;': '"',  # Double quote
                }
                for entity, char in entity_map.items():
                    processed_value = processed_value.replace(entity, char)

            processed_placeholders[key] = processed_value

        # Replace placeholders in paragraphs
        for paragraph in doc.paragraphs:
            replace_text_in_paragraph(paragraph, processed_placeholders)

        # Replace placeholders in tables
        for table in doc.tables:
            for row in table.rows:
                for cell in row.cells:
                    for paragraph in cell.paragraphs:
                        replace_text_in_paragraph(paragraph, processed_placeholders)

        # Save the document
        logger.info(f"Saving document to: {output_path}")
        try:
            # Try to save the document
            doc.save(output_path)
        except PermissionError:
            # If the file is already open, save to a temporary file and then rename it
            logger.warning(f"Permission denied when saving to {output_path}, trying with a temporary file")
            temp_path = f"{output_path}.temp"
            doc.save(temp_path)

            # Try to remove the original file if it exists
            try:
                if os.path.exists(output_path):
                    os.remove(output_path)
            except Exception as e:
                logger.error(f"Error removing original file: {e}")
                return None

            # Rename the temporary file to the original file name
            try:
                os.rename(temp_path, output_path)
            except Exception as e:
                logger.error(f"Error renaming temporary file: {e}")
                return None

        # Verify the document was created
        if os.path.exists(output_path):
            logger.info(f"Document generated successfully: {output_path}")
            return output_path
        else:
            logger.error(f"Document generation failed: Output file not found at {output_path}")
            return None
    except Exception as e:
        logger.error(f"Error replacing placeholders in document: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        return None

def replace_text_in_paragraph(paragraph, placeholders):
    """
    Replace placeholders in a paragraph while preserving formatting including highlights.

    Args:
        paragraph: The paragraph to process
        placeholders (dict): Dictionary of placeholders and values
    """
    if not paragraph.text:
        return

    # First, check if the paragraph contains any placeholders
    contains_placeholder = False
    for key in placeholders.keys():
        # Allow optional whitespace inside the curly braces, e.g., {{ key }}
        pattern = r'\{\{\s*' + re.escape(key) + r'\s*\}\}'
        if re.search(pattern, paragraph.text):
            contains_placeholder = True
            break

    # If no placeholders found, return early
    if not contains_placeholder:
        return

    # PRESERVE FORMATTING: Replace text in individual runs to maintain formatting
    for run in paragraph.runs:
        if not run.text:
            continue

        # Replace placeholders in this run's text
        modified_text = run.text
        for key, value in placeholders.items():
            pattern = r'\{\{\s*' + re.escape(key) + r'\s*\}\}'
            if re.search(pattern, modified_text):
                # Store current formatting before replacement
                current_font = run.font
                current_highlight = current_font.highlight_color

                # Replace the placeholder
                modified_text = re.sub(pattern, str(value), modified_text)

                # Update the run text
                run.text = modified_text

                # PRESERVE HIGHLIGHTS: Restore highlight if it existed
                if current_highlight is not None:
                    run.font.highlight_color = current_highlight

                logger.info(f"Replaced placeholder in run while preserving formatting: {key}")

    # If placeholders span multiple runs, we need a more sophisticated approach
    # Check if any placeholders are still unresolved
    remaining_text = paragraph.text
    for key in placeholders.keys():
        pattern = r'\{\{' + re.escape(key) + r'\}\}'
        if re.search(pattern, remaining_text):
            # Fallback: Replace in full text but try to preserve some formatting
            logger.warning(f"Placeholder {key} spans multiple runs, using fallback replacement")
            full_text = paragraph.text
            for placeholder_key, value in placeholders.items():
                placeholder_pattern = r'\{\{' + re.escape(placeholder_key) + r'\}\}'
                full_text = re.sub(placeholder_pattern, str(value), full_text)

            # Only clear and replace if we absolutely have to
            if full_text != paragraph.text:
                # Try to preserve formatting of the first run
                first_run_font = None
                first_run_highlight = None
                if paragraph.runs:
                    first_run_font = paragraph.runs[0].font
                    first_run_highlight = first_run_font.highlight_color

                # Clear and replace
                for run in paragraph.runs:
                    run.text = ""

                if paragraph.runs:
                    paragraph.runs[0].text = full_text
                    # Restore formatting if possible
                    if first_run_highlight is not None:
                        paragraph.runs[0].font.highlight_color = first_run_highlight
                else:
                    new_run = paragraph.add_run(full_text)
                    if first_run_highlight is not None:
                        new_run.font.highlight_color = first_run_highlight
            break
