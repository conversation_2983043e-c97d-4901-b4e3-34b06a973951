"""
BEC screen UI for PACE application.
"""

import sys
from PySide6.QtWidgets import (
    QW<PERSON>t, QLabel, QLineEdit, QComboBox, QCheckBox, QSpinBox,
    QPushButton, QVBoxLayout, QHBoxLayout, QFormLayout, QGroupBox,
    QRadioButton, QButtonGroup, QApplication, QMessageBox
)
from PySide6.QtCore import Qt, Signal

from models.client import Client
from models.bec import BECEngagement

class BECScreen(QWidget):
    """
    BEC screen for the PACE application.
    """

    # Signal emitted when the user clicks the Generate Documents button
    generate_clicked = Signal(BECEngagement)

    # Signal emitted when the user clicks the Back button
    back_clicked = Signal()

    def __init__(self, client):
        """
        Initialize the BEC screen.

        Args:
            client (Client): The client for this engagement
        """
        super().__init__()

        self.client = client
        self.engagement = BECEngagement(client)

        self.init_ui()

    def init_ui(self):
        """
        Initialize the UI.
        """
        # Set up the main layout
        main_layout = QVBoxLayout()

        # Add the title
        title_label = QLabel("Business Email Compromise")
        title_label.setStyleSheet("font-size: 18px; font-weight: bold;")
        main_layout.addWidget(title_label)

        # Add the client information
        client_info_label = QLabel(f"Client: {self.client.name}")
        client_info_label.setStyleSheet("font-weight: bold;")
        main_layout.addWidget(client_info_label)

        # Add a separator
        main_layout.addSpacing(20)

        # Create a form layout for the BEC parameters
        form_layout = QFormLayout()

        # Add the email platform dropdown
        self.platform_combo = QComboBox()
        self.platform_combo.addItems(["M365", "Google Cloud Platform", "Exchange", "Other", "Hybrid"])
        self.platform_combo.currentTextChanged.connect(self.on_platform_changed)
        form_layout.addRow("Email Platform:", self.platform_combo)

        # Add the tenant size field
        self.tenant_size_spin = QSpinBox()
        self.tenant_size_spin.setRange(1, 100000)
        self.tenant_size_spin.setValue(500)
        form_layout.addRow("Enter the size of the tenant:", self.tenant_size_spin)

        # Add the mailbox count field
        self.mailbox_count_spin = QSpinBox()
        self.mailbox_count_spin.setRange(1, 100)
        self.mailbox_count_spin.setValue(5)
        self.mailbox_count_spin.valueChanged.connect(self.on_mailbox_count_changed)
        form_layout.addRow("Enter the number of mailboxes affected:", self.mailbox_count_spin)

        # Add the E5 license checkbox
        self.e5_check = QCheckBox("Tenant has E5 licenses")
        form_layout.addRow("", self.e5_check)

        # Add the message extraction checkbox and cost field
        message_extraction_layout = QHBoxLayout()

        self.message_extraction_check = QCheckBox("Include Message Extraction")
        self.message_extraction_check.stateChanged.connect(self.on_message_extraction_check_changed)

        self.message_extraction_cost_spin = QSpinBox()
        self.message_extraction_cost_spin.setRange(1, 100000)
        self.message_extraction_cost_spin.setValue(1000)
        self.message_extraction_cost_spin.setPrefix("$")
        self.message_extraction_cost_spin.setSingleStep(500)
        self.message_extraction_cost_spin.setEnabled(False)
        self.message_extraction_cost_spin.valueChanged.connect(self.on_message_extraction_cost_changed)

        message_extraction_layout.addWidget(self.message_extraction_check)
        message_extraction_layout.addWidget(QLabel("Cost:"))
        message_extraction_layout.addWidget(self.message_extraction_cost_spin)

        form_layout.addRow("", message_extraction_layout)

        # Add the triage analysis checkbox and count field
        triage_analysis_layout = QHBoxLayout()

        self.triage_analysis_check = QCheckBox("Include Triage Analysis")
        self.triage_analysis_check.stateChanged.connect(self.on_triage_analysis_check_changed)

        self.triage_count_spin = QSpinBox()
        self.triage_count_spin.setRange(1, 15)  # Max 15 mailboxes
        self.triage_count_spin.setValue(1)
        self.triage_count_spin.setSingleStep(1)
        self.triage_count_spin.setEnabled(False)
        self.triage_count_spin.valueChanged.connect(self.on_triage_count_changed)

        triage_analysis_layout.addWidget(self.triage_analysis_check)
        triage_analysis_layout.addWidget(QLabel("Count:"))
        triage_analysis_layout.addWidget(self.triage_count_spin)

        form_layout.addRow("", triage_analysis_layout)

        # Add the form layout to the main layout
        main_layout.addLayout(form_layout)

        # Add a separator
        main_layout.addSpacing(10)

        # Add document options group box
        doc_options_group = QGroupBox("Document Options")
        doc_options_layout = QVBoxLayout()

        # Add BAA checkbox
        self.baa_check = QCheckBox("Include Business Associate Agreement (BAA)")
        self.baa_check.setToolTip("Check this if you need a Business Associate Agreement")
        doc_options_layout.addWidget(self.baa_check)

        # Add DPA checkbox
        self.dpa_check = QCheckBox("Include Data Processing Agreement (DPA)")
        self.dpa_check.setToolTip("Check this if you need a Data Processing Agreement")
        doc_options_layout.addWidget(self.dpa_check)

        doc_options_group.setLayout(doc_options_layout)
        main_layout.addWidget(doc_options_group)

        # Add a separator
        main_layout.addSpacing(10)

        # Add the buttons
        button_layout = QHBoxLayout()

        self.back_button = QPushButton("Back")
        self.back_button.clicked.connect(self.on_back_clicked)

        self.generate_button = QPushButton("Generate Documents")
        self.generate_button.clicked.connect(self.on_generate_clicked)

        button_layout.addWidget(self.back_button)
        button_layout.addStretch()
        button_layout.addWidget(self.generate_button)

        main_layout.addLayout(button_layout)

        # Set the main layout
        self.setLayout(main_layout)

        # Set the window properties
        self.setWindowTitle("PACE - Business Email Compromise")
        self.resize(600, 400)

        # Initialize the UI state based on the current platform
        self.on_platform_changed(self.platform_combo.currentText())

    def on_platform_changed(self, platform):
        """
        Handle changes to the email platform dropdown.

        Args:
            platform (str): The selected email platform
        """
        # Hide E5 license and message extraction for non-M365 platforms
        is_m365 = platform == "M365"

        # Get the form layout that contains the rows we need to modify
        form_layout = None
        for i in range(self.layout().count()):
            item = self.layout().itemAt(i)
            if isinstance(item, QFormLayout):
                form_layout = item
                break

        if form_layout:
            # Handle E5 license checkbox visibility
            for i in range(form_layout.rowCount()):
                field_item = form_layout.itemAt(i, QFormLayout.FieldRole)
                if field_item and isinstance(field_item.widget(), QCheckBox) and field_item.widget().text() == "Tenant has E5 licenses":
                    # Found the E5 license checkbox
                    field_item.widget().setVisible(is_m365)
                    # Uncheck if not M365
                    if not is_m365:
                        field_item.widget().setChecked(False)
                    break

            # Handle message extraction visibility
            for i in range(form_layout.rowCount()):
                field_item = form_layout.itemAt(i, QFormLayout.FieldRole)
                if field_item and isinstance(field_item.layout(), QHBoxLayout):
                    for j in range(field_item.layout().count()):
                        widget = field_item.layout().itemAt(j).widget()
                        if widget and isinstance(widget, QCheckBox) and widget.text() == "Include Message Extraction":
                            # Found the message extraction row
                            if not is_m365:
                                # Hide the message extraction option for non-M365
                                widget.setVisible(False)
                                # Also hide the cost label and spinbox
                                for k in range(field_item.layout().count()):
                                    other_widget = field_item.layout().itemAt(k).widget()
                                    if other_widget:
                                        other_widget.setVisible(False)
                                # Uncheck the message extraction checkbox
                                widget.setChecked(False)
                            else:
                                # Show the message extraction option for M365
                                widget.setVisible(True)
                                # Also show the cost label and spinbox
                                for k in range(field_item.layout().count()):
                                    other_widget = field_item.layout().itemAt(k).widget()
                                    if other_widget:
                                        other_widget.setVisible(True)
                            break

    def on_mailbox_count_changed(self, value):
        """
        Handle changes to the mailbox count field.

        Args:
            value (int): The new mailbox count
        """
        if value > 15:
            QMessageBox.warning(
                self,
                "Warning",
                "Please contact the Business Email Compromise lead to determine pricing for more than 15 mailboxes."
            )

    def on_message_extraction_check_changed(self, state):
        """
        Handle changes to the message extraction checkbox.

        Args:
            state (int): The checkbox state
        """
        is_checked = state == Qt.Checked
        self.message_extraction_cost_spin.setEnabled(is_checked)

        # Update the engagement object
        self.engagement.include_message_extraction = is_checked
        self.engagement.message_extraction_cost = self.message_extraction_cost_spin.value() if is_checked else 0

    def on_triage_analysis_check_changed(self, state):
        """
        Handle changes to the triage analysis checkbox.

        Args:
            state (int): The checkbox state
        """
        is_checked = state == Qt.Checked
        self.triage_count_spin.setEnabled(is_checked)

        # Update the engagement object
        self.engagement.include_triage_analysis = is_checked
        self.engagement.triage_count = self.triage_count_spin.value() if is_checked else 0

    def on_message_extraction_cost_changed(self, value):
        """
        Handle changes to the message extraction cost field.

        Args:
            value (int): The new message extraction cost
        """
        if self.message_extraction_check.isChecked():
            self.engagement.message_extraction_cost = value

    def on_triage_count_changed(self, value):
        """
        Handle changes to the triage count field.

        Args:
            value (int): The new triage count
        """
        if self.triage_analysis_check.isChecked():
            self.engagement.triage_count = value

    def on_back_clicked(self):
        """
        Handle the Back button click.
        """
        self.back_clicked.emit()

    def on_generate_clicked(self):
        """
        Handle the Generate Documents button click.
        """
        # Update the engagement object with the form values
        self.engagement.email_platform = self.platform_combo.currentText()
        self.engagement.tenant_size = self.tenant_size_spin.value()
        self.engagement.mailbox_count = self.mailbox_count_spin.value()

        # E5 license is only applicable for M365
        if self.engagement.email_platform == "M365":
            self.engagement.is_e5_license = self.e5_check.isChecked()
        else:
            self.engagement.is_e5_license = False

        # Message extraction is only available for M365
        if self.engagement.email_platform == "M365":
            self.engagement.include_message_extraction = self.message_extraction_check.isChecked()
            self.engagement.message_extraction_cost = self.message_extraction_cost_spin.value() if self.message_extraction_check.isChecked() else 0
        else:
            self.engagement.include_message_extraction = False
            self.engagement.message_extraction_cost = 0

        self.engagement.include_triage_analysis = self.triage_analysis_check.isChecked()
        self.engagement.triage_count = self.triage_count_spin.value() if self.triage_analysis_check.isChecked() else 0

        # Update the client with document options
        self.client.needs_baa = self.baa_check.isChecked()
        self.client.needs_dpa = self.dpa_check.isChecked()

        # Emit the generate_clicked signal
        self.generate_clicked.emit(self.engagement)


if __name__ == "__main__":
    app = QApplication(sys.argv)
    client = Client("Test Client", "123 Main St", "AIG", "Baker & Hostetler LLP")
    bec_screen = BECScreen(client)
    bec_screen.show()
    sys.exit(app.exec())
