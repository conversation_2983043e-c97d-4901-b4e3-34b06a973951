#!/usr/bin/env python3
import os, sys
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from models.client import Client
from models.dfir import DFIREngagement

client = Client("Chubb Test", "Addr", "Chubb", "Wilson Elser")
eng = DFIREngagement(client)
eng.endpoint_count = 250
eng.email_platform = "M365"
eng.edr_monitoring = "Monitoring by Client"
eng.is_fixed_fee = False

ph = eng.get_placeholders()
keys = [
    'cs410_hours','cs120_forensic_hours','cs120_email_hours','cs210_hours','cs430_hours',
    'chubb_phase1_hours','chubb_phase2_hours','chubb_phase3_hours','chubb_phase4_hours','chubb_report_hours',
    'chubb_total_hours','total_hours','Estimated Labor Hours',
    'total_labor_costs','total_costs','chubb_costs','chubb_total_costs',
    'dfir_rate'
]
for k in keys:
    print(f"{k}: {ph.get(k)}")

