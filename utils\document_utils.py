"""
Document utilities for PACE application.
This module provides helper functions for document generation and validation.
"""

import os
import shutil
import logging
from contextlib import contextmanager

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Track temporary files for cleanup
temporary_files = []

class DocumentGenerationError(Exception):
    """Exception raised for errors in the document generation process."""
    pass

def validate_template(template_path):
    """
    Validate that a template file exists and is a valid Office document.
    
    Args:
        template_path (str): Path to the template file
        
    Returns:
        bool: True if the template is valid, False otherwise
    """
    try:
        if not os.path.exists(template_path):
            logger.error(f"Template file not found: {template_path}")
            return False
            
        # Check if it's a valid Office Open XML file
        with open(template_path, 'rb') as f:
            content = f.read(4)  # Read first 4 bytes
            if not content.startswith(b'PK'):
                logger.error(f"Template is not a valid Office document: {template_path}")
                return False
                
        # Try to open it with python-docx
        try:
            from docx import Document
            doc = Document(template_path)
            # Check if it has at least one paragraph or table
            if len(doc.paragraphs) == 0 and len(doc.tables) == 0:
                logger.warning(f"Template appears to be empty: {template_path}")
            return True
        except Exception as e:
            logger.error(f"Failed to open template with python-docx: {e}")
            return False
            
    except Exception as e:
        logger.error(f"Error validating template {template_path}: {e}")
        return False

def save_document_safely(doc, output_path):
    """
    Save a document and verify its integrity.
    
    Args:
        doc: The document object to save
        output_path (str): Path where the document should be saved
        
    Returns:
        bool: True if the document was saved successfully, False otherwise
    """
    try:
        # Create a backup of the output file if it exists
        if os.path.exists(output_path):
            backup_path = output_path + ".bak"
            shutil.copy2(output_path, backup_path)
            logger.info(f"Created backup of existing output file: {backup_path}")
            
        # Create the output directory if it doesn't exist
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        
        # Save the document
        doc.save(output_path)
        
        # Verify the document was saved correctly
        if not os.path.exists(output_path):
            logger.error(f"Document was not saved: {output_path}")
            return False
            
        # Verify it's a valid Office document
        try:
            with open(output_path, 'rb') as f:
                content = f.read(4)
                if not content.startswith(b'PK'):
                    logger.error(f"Generated document is not a valid Office document: {output_path}")
                    return False
        except Exception as e:
            logger.error(f"Error verifying document structure: {e}")
            return False
            
        # Try to open it with python-docx to verify integrity
        try:
            from docx import Document
            test_doc = Document(output_path)
            return True
        except Exception as e:
            logger.error(f"Generated document failed integrity check: {e}")
            return False
            
    except Exception as e:
        logger.error(f"Error saving document to {output_path}: {e}")
        return False

def verify_document_structure(doc_path):
    """
    Verify that a document has a valid Office Open XML structure.
    
    Args:
        doc_path (str): Path to the document
        
    Returns:
        bool: True if the document has a valid structure, False otherwise
    """
    try:
        if not os.path.exists(doc_path):
            logger.error(f"Document not found: {doc_path}")
            return False
            
        with open(doc_path, 'rb') as f:
            content = f.read(4)
            if not content.startswith(b'PK'):
                logger.error(f"Not a valid Office Open XML file: {doc_path}")
                return False
        return True
    except Exception as e:
        logger.error(f"Error verifying document structure: {e}")
        return False

def recover_from_failed_generation(output_path, backup_path):
    """
    Recover from a failed document generation by restoring from backup.
    
    Args:
        output_path (str): Path to the output file
        backup_path (str): Path to the backup file
        
    Returns:
        bool: True if recovery was successful, False otherwise
    """
    try:
        if os.path.exists(backup_path):
            shutil.copy2(backup_path, output_path)
            logger.info(f"Recovered document from backup: {backup_path} -> {output_path}")
            return True
        else:
            logger.error(f"Backup file not found: {backup_path}")
            return False
    except Exception as e:
        logger.error(f"Error recovering from backup: {e}")
        return False

@contextmanager
def managed_document(path):
    """
    Context manager for working with documents.
    
    Args:
        path (str): Path to the document
        
    Yields:
        Document: The document object
    """
    from docx import Document
    doc = None
    try:
        doc = Document(path)
        yield doc
    finally:
        if doc:
            try:
                doc.save(path)
            except Exception as e:
                logger.error(f"Error saving document in context manager: {e}")

def cleanup_resources():
    """
    Clean up temporary files created during document generation.
    """
    for temp_file in temporary_files:
        try:
            if os.path.exists(temp_file):
                os.remove(temp_file)
                logger.info(f"Removed temporary file: {temp_file}")
        except OSError as e:
            logger.error(f"Error removing temporary file {temp_file}: {e}")
