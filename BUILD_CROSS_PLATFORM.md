# PACE Cross-Platform Build Guide v1.1.5

This guide explains how to build PACE for different operating systems using the new cross-platform build script introduced in version 1.1.5.

## Overview

PACE now supports building for multiple platforms:
- **Windows**: Creates `.exe` executable + Inno Setup installer
- **macOS**: Creates `.app` bundle + DMG installer
- **Linux**: Creates executable binary

## Build Scripts

### 1. `build_executable.py` (Original - Windows Focus)
- **Purpose**: Windows-optimized build with Inno Setup installer
- **Output**: `PACE.exe` + Windows installer
- **Use when**: Building for Windows deployment

### 2. `build_cross_platform.py` (New - Multi-Platform)
- **Purpose**: Automatic platform detection and appropriate build
- **Output**: Platform-specific executable + installer
- **Use when**: Building for macOS, Linux, or cross-platform development

## Prerequisites

### All Platforms
- Python 3.8+
- PySide6
- PyInstaller (automatically installed by build scripts)

### Windows
- Icon file: `PACE.ico`
- For installer: Inno Setup (optional)

### macOS
- Icon file: `PACE.icns` (convert from .ico using online tools or `sips`)
- Xcode Command Line Tools (for DMG creation)

### Linux
- Icon file: `PACE.png`
- File manager (xdg-open, nautilus, etc.)

## Building Instructions

### Quick Start (Recommended)
```bash
# Use the cross-platform script - automatically detects your OS
python build_cross_platform.py
```

### Platform-Specific Instructions

#### Windows
```bash
# Option 1: Use cross-platform script
python build_cross_platform.py

# Option 2: Use Windows-specific script (includes Inno Setup)
python build_executable.py
```

#### macOS
```bash
# Ensure you have PACE.icns icon file
python build_cross_platform.py
```

#### Linux
```bash
# Ensure you have PACE.png icon file
python build_cross_platform.py
```

## Icon File Conversion

### Windows .ico to macOS .icns
```bash
# Using macOS built-in tool
sips -s format icns PACE.ico --out PACE.icns

# Or use online converters like cloudconvert.com
```

### Windows .ico to Linux .png
```bash
# Extract largest size from .ico file
# Use online converters or image editing software
```

## Output Files

### Windows
- `dist/PACE.exe` - Standalone executable
- `PACE_installer.iss` - Inno Setup script (from build_executable.py)

### macOS
- `dist/PACE.app` - Application bundle
- `PACE_v1.1.5_macOS.dmg` - Disk image installer

### Linux
- `dist/PACE` - Executable binary

## What's New in v1.1.5

### 🚀 Cross-Platform Support
PACE v1.1.5 introduces comprehensive cross-platform support, making it easy to build and deploy on Windows, macOS, and Linux without manual code modifications.

### 🔧 New Features

#### 1. **Automatic Platform Detection**
- The build script automatically detects your operating system
- Applies platform-specific configurations automatically
- No more manual switching between different build processes

#### 2. **Cross-Platform Directory Opening**
- **Before v1.1.5**: Manual code commenting/uncommenting for different platforms
- **Now**: Automatic detection and appropriate system command usage:
  - Windows: `explorer`
  - macOS: `open` (Finder)
  - Linux: `xdg-open` or available file manager

#### 3. **Platform-Specific Build Outputs**
- **Windows**:
  - `.exe` executable with version info
  - Inno Setup installer support (via original build script)
  - Windows-specific icon format (.ico)
- **macOS**:
  - Proper `.app` bundle with Info.plist
  - DMG disk image installer
  - macOS-specific icon format (.icns)
  - Code signing preparation
- **Linux**:
  - Standard executable binary
  - PNG icon support
  - File manager integration

#### 4. **Dual Build System**
- **`build_executable.py`**: Original Windows-focused script (unchanged)
- **`build_cross_platform.py`**: New universal script for all platforms

#### 5. **Enhanced Developer Experience**
- No more platform-specific code modifications
- Automatic icon format handling
- Better error messages and guidance
- Comprehensive documentation

## Key Improvements

### 1. Seamless Cross-Platform Development
Developers can now work on any platform without worrying about platform-specific build configurations.

### 2. Professional Distribution
Each platform gets its native installer format:
- Windows: Professional Inno Setup installer
- macOS: Standard DMG disk image
- Linux: Portable executable

### 3. Maintained Compatibility
All existing Windows functionality is preserved while adding new cross-platform capabilities.

## Troubleshooting

### Icon File Missing
```
❌ Error: PACE.icns not found!
```
**Solution**: Convert your PACE.ico to the appropriate format for your platform.

### PyInstaller Not Found
```
Installing PyInstaller...
```
**Solution**: The script automatically installs PyInstaller. Ensure you have internet access.

### Build Fails
1. Check that `main.py` exists in the current directory
2. Ensure all dependencies are installed: `pip install -r requirements.txt`
3. Check the error output for specific issues

### DMG Creation Fails (macOS)
```
✗ Failed to create DMG
```
**Solution**: Ensure you have Xcode Command Line Tools installed:
```bash
xcode-select --install
```

## Testing Your Build

### Windows
```bash
# Test the executable
dist/PACE.exe

# Test installer (if using build_executable.py)
# Right-click PACE_installer.iss → Compile with Inno Setup
```

### macOS
```bash
# Test the app bundle
open dist/PACE.app

# Test DMG installer
open PACE_v1.1.5_macOS.dmg
```

### Linux
```bash
# Test the executable
./dist/PACE
```

## Distribution

### Windows
- Distribute `PACE.exe` for standalone use
- Use Inno Setup installer for professional deployment

### macOS
- Distribute `PACE_v1.1.5_macOS.dmg` for easy installation
- Users can drag PACE.app to Applications folder

### Linux
- Distribute the `PACE` executable
- Consider creating a .deb or .rpm package for specific distributions

## Notes

- The original `build_executable.py` remains unchanged and fully functional for Windows builds
- The new cross-platform approach maintains all existing functionality
- Directory opening now works automatically on all platforms without manual code changes
- All platform-specific optimizations are preserved
