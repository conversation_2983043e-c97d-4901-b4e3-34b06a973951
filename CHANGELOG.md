# PACE Changelog

All notable changes to the PACE (Process Automation for Client Engagements) application will be documented in this file.

## [1.21] - 2025-08-26

### Changed
- Updated TACI SOW hours from 60 total to 45 total hours
  - Phase 1 hours: 25 hours (TA Comms)
  - Phase 2 hours: 20 hours (TA Comms)
  - Total hours now 45 (25 + 20) for all TACI engagement types

### Fixed
- Fixed formatting issues with templates
- Improved template consistency and layout

---

## [1.20] - 2025-08-19

### Major Performance Optimizations

#### Document Generation Consolidation
- Consolidated 3 duplicate document generation functions
- Reduced document generation code through consolidation
- Improved startup time and document generation speed

#### Code Performance Improvements
- Moved all imports to module level for better performance

### UI Improvements (DFIR and R&R)

- Recovery & Remediation assistance selection is now an active, guided experience:
  - Assistance Type: Remote or Onsite, plus Number of Resources
  - Section appears only when “Recovery and Remediation Needed” is selected under Additional Services
  - Dropdown/choice-driven content in generated contracts is now honored automatically — no more deleting highlighted placeholders by hand
- Polished R&R controls for better clarity and speed:
  - Clearer labels and consistent enable/disable behavior
  - Resource count editable for both assistance types

### Document Generation
- RR SOW now generates automatically when R&R is selected in DFIR (include_rr), removing a prior extra step
- Centralized generation pipeline continues to ensure reliable output and section removal where applicable

### Quality and Maintenance
- Version bumped to 1.20 across app, installer, and resource manifests
- Verified template paths and resource packaging for installer
- General cleanup of redundant logic paths and improved visibility updates in the DFIR UI

### Technical Improvements

#### Model Updates
- **Added**: `generate_rr_sow` attribute to `DFIREngagement` model
- **Fixed**: R&R template paths to match actual file names

#### Document Generation Logic
- **Updated**: Both `DocumentGenerator` and `ReliableDocumentGenerator` classes
- **Preserved**: All existing R&R pricing and placeholder logic

#### Code Quality
- Fixed Template path inconsistencies
- Comprehensive test suite for R&R conditional generation
- Improved: Error handling and logging

### User Interface Improvements
- **Better layout**: R&R options now clearly separated
- **Clearer labels**: More descriptive checkbox and field names
- **Improved workflow**: Logical progression from need assessment to document generation
- **Enhanced usability**: Users can explore R&R options without committing to document generation

---

## [1.1.3] - Previous Release

### Added
- Added mandatory $599.00 fee to all Chubb SOWs (both DFIR and BEC)
- Fixed proper placeholder replacement for chubb_endpoint_count in Chubb SOW templates
- Fixed template path resolution to prioritize templates from the base_templates directory
- Improved error handling in document generation

### Fixed
- Template path resolution issues
- Chubb-specific placeholder handling
- Document generation error handling

---

## [1.1.2] - Previous Release

### Fixed
- Fixed template path resolution in installed version
- Improved HTML entity decoding for special characters
- Fixed issues with TACI and IR SOW documents not opening
- Enhanced error handling and logging for document generation

---

## [1.1.1] - Previous Release

### Fixed
- Fixed Chubb template issues with CS code placeholders
- Improved performance with cleanup of unnecessary files
- Updated documentation and README

---

## [1.1.0] - Previous Release

### Added
- Added DFIR engagement support
- Improved carrier-specific features
- Added Beazley-specific ransomware/network intrusion option
- Enhanced document generation with better error handling
- Improved template handling

---

## [1.0.0] - Initial Release

### Added
- Initial release with basic document generation capabilities
- Support for TACI, BEC, and Recovery & Remediation engagements
- Basic carrier and law firm integration

---

## Performance Metrics (v1.1.4)

| Metric | Before | After | Improvement |
|--------|--------|-------|-------------|
| **Document Generation Functions** | 3 separate | 1 centralized | 67% reduction |
| **Duplicate Import Statements** | 16+ | 0 | 100% elimination |
| **Duplicate Code Blocks** | 7+ | 1 | 86% reduction |
| **Fallback Logic Patterns** | 3 identical | 1 centralized | 67% reduction |
| **Test Import Overhead** | Per-function | Module-level | ~50% faster |

## Migration Notes

### For Users
- **R&R Workflow Change**: Users now need to explicitly check "Generate R&R SOW" to create R&R documents
- **No Breaking Changes**: All existing functionality preserved
- **Better Performance**: Faster startup and document generation

### For Developers
- **Import Changes**: All document generation imports moved to module level
- **New Model Attribute**: `generate_rr_sow` added to `DFIREngagement`
- **Centralized Functions**: Use `_generate_document_with_docxtpl()` for document generation
- **Test Updates**: Test files now use module-level imports for better performance

## Planned Future Enhancements

- **Business Email Compromise (BEC) completion** - Finalize remaining BEC engagement features
- **Threat Actor Communications (TACI) enhancements** - Expand TACI capabilities and workflows
- **Advanced template management** - Improved template organization and customization
- **Enhanced reporting** - Better analytics and engagement tracking
- **Workflow automation** - Additional process automation features
