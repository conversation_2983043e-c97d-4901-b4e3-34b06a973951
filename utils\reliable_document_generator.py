"""
Reliable document generator for PACE application.
This module provides a reliable approach to document generation that works consistently.
"""

import os
import shutil
import logging
import traceback
import html
import re
from docxtpl import DocxTemplate
import jinja2
from utils.docx_placeholder_replacer import replace_placeholders_in_docx
from utils.path_resolver import resolve_template_path, get_all_template_files
from docx import Document

# Set up logging to user's temp directory to avoid permission issues
import tempfile
log_file = os.path.join(tempfile.gettempdir(), "pace_document_generation.log")
logging.basicConfig(level=logging.INFO,  # Changed to INFO to reduce log size
                    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
                    handlers=[
                        logging.FileHandler(log_file),
                        logging.StreamHandler()
                    ])
logger = logging.getLogger(__name__)

def generate_document_reliable(template_path, output_path, placeholders, engagement=None):
    """
    Generate a document using DocxTemplate with a reliable approach.

    Args:
        template_path (str): Path to the template file
        output_path (str): Path to save the generated document
        placeholders (dict): Placeholders to replace in the template
        engagement: Optional engagement object for section removal

    Returns:
        str: Path to the generated document, or None if generation failed
    """
    try:
        # Log the function call
        logger.info(f"Generating document from {template_path} to {output_path}")

        # Sanitize output path to ensure it's valid
        output_dir = os.path.dirname(output_path)
        output_filename = os.path.basename(output_path)
        # Replace any invalid characters in the filename
        output_filename = re.sub(r'[\\/*?:"<>|]', '_', output_filename)
        # Limit filename length
        if len(output_filename) > 100:
            output_filename = output_filename[:97] + '...'
        sanitized_output_path = os.path.join(output_dir, output_filename)

        if sanitized_output_path != output_path:
            logger.info(f"Sanitized output path from {output_path} to {sanitized_output_path}")
            output_path = sanitized_output_path

        # Resolve the template path for bundled environments
        resolved_template_path = resolve_template_path(template_path)
        logger.info(f"Resolved template path: {resolved_template_path}")

        # Check if the template exists
        if not os.path.exists(resolved_template_path):
            logger.error(f"Template not found at resolved path: {resolved_template_path}")

            # List all available templates for debugging
            logger.info("Available templates:")
            for template_file in get_all_template_files():
                logger.info(f"  - {template_file}")

            return None

        # Create the output directory if it doesn't exist
        os.makedirs(os.path.dirname(output_path), exist_ok=True)

        # Load the template
        logger.info(f"Loading template: {resolved_template_path}")
        try:
            doc = DocxTemplate(resolved_template_path)
        except Exception as template_error:
            logger.error(f"Error loading template: {str(template_error)}")
            # Try a direct copy as a fallback
            logger.info(f"Attempting direct copy fallback due to template loading error")
            shutil.copy2(resolved_template_path, output_path)
            if os.path.exists(output_path):
                logger.info(f"Created a direct copy at {output_path} as a fallback")
                return output_path
            return None

        # Create a custom Jinja2 environment to handle ampersands correctly
        logger.info("Creating custom Jinja2 environment")
        custom_jinja_env = jinja2.Environment(autoescape=False)
        doc.jinja_env = custom_jinja_env

        # Process placeholders to handle ampersands and special cases
        processed_placeholders = {}
        for key, value in placeholders.items():
            # Handle RichText objects
            if hasattr(value, '__class__') and value.__class__.__name__ == 'RichText':
                # Pass RichText objects through as-is
                processed_placeholders[key] = value
                logger.info(f"Preserving RichText object for {key}")
                continue

            # Convert to string if not already
            if value is None:
                processed_value = ""
            elif not isinstance(value, str):
                processed_value = str(value)
            else:
                processed_value = value

            # Decode HTML entities if present
            if isinstance(processed_value, str):
                # Decode HTML entities
                processed_value = html.unescape(processed_value)
                # Handle specific HTML entities that might not be caught
                entity_map = {
                    '&#x27;': "'",  # Apostrophe
                    '&apos;': "'",  # Apostrophe
                    '&quot;': '"',  # Double quote
                    '&amp;': '&',   # Ampersand
                    '&lt;': '<',    # Less than
                    '&gt;': '>',    # Greater than
                    '&#39;': "'",  # Apostrophe (decimal)
                    '&#34;': '"',  # Double quote (decimal)
                    '&#38;': '&',   # Ampersand (decimal)
                    '&#60;': '<',   # Less than (decimal)
                    '&#62;': '>',   # Greater than (decimal)
                }
                for entity, char in entity_map.items():
                    processed_value = processed_value.replace(entity, char)

                # Remove any XML tags that might be in the value
                processed_value = re.sub(r'<[^>]+>', '', processed_value)

                # Special handling for law firm names with ampersands
                if '&' in processed_value and key in ['law_firm', 'lawfirm', 'Lawfirm', 'firm', 'counsel']:
                    from docxtpl import RichText
                    rt = RichText()
                    rt.add(processed_value)
                    processed_placeholders[key] = rt
                    logger.info(f"Converting string with ampersand to RichText for {key}: {processed_value}")
                    continue

            processed_placeholders[key] = processed_value

        # Replace placeholders
        logger.info("Rendering template with placeholders")
        doc.render(processed_placeholders)

        # Save the document with error handling
        logger.info(f"Saving document to: {output_path}")
        try:
            # Create a temporary file first
            temp_output_path = output_path + ".tmp"
            doc.save(temp_output_path)

            # Verify the temporary file was created and is valid
            if os.path.exists(temp_output_path):
                # Check if it's a valid Office document
                with open(temp_output_path, 'rb') as f:
                    content = f.read(4)
                    if not content.startswith(b'PK'):
                        logger.error(f"Generated document is not a valid Office document")
                        # Try a direct copy as a fallback
                        logger.info(f"Attempting direct copy fallback due to invalid document")
                        shutil.copy2(resolved_template_path, output_path)
                        if os.path.exists(output_path):
                            logger.info(f"Created a direct copy at {output_path} as a fallback")
                            return output_path
                        return None

                # Move the temporary file to the final location
                if os.path.exists(output_path):
                    os.remove(output_path)  # Remove existing file if it exists
                shutil.move(temp_output_path, output_path)

                logger.info(f"Document generated successfully: {output_path}")
                return output_path
            else:
                logger.error(f"Document generation failed: Temporary file not created at {temp_output_path}")
                return None
        except Exception as save_error:
            logger.error(f"Error saving document: {str(save_error)}")
            logger.error(traceback.format_exc())

            # Try a direct copy as a fallback
            logger.info(f"Attempting direct copy fallback due to save error")
            try:
                shutil.copy2(resolved_template_path, output_path)
                if os.path.exists(output_path):
                    logger.info(f"Created a direct copy at {output_path} as a fallback")
                    return output_path
            except Exception as copy_error:
                logger.error(f"Error creating fallback copy: {str(copy_error)}")

            return None
    except Exception as e:
        logger.error(f"Error generating document: {str(e)}")
        logger.error(traceback.format_exc())

        # Try a simple copy as a fallback
        try:
            logger.info(f"Attempting fallback: copying template to output")
            # If the template exists but can't be rendered, create a copy
            if os.path.exists(resolved_template_path):
                # Create the output directory if it doesn't exist
                os.makedirs(os.path.dirname(output_path), exist_ok=True)

                # Copy the template to the output path
                shutil.copy2(resolved_template_path, output_path)
                logger.info(f"Created a copy of the template at {output_path} as a fallback")

                # Verify the document was created successfully
                if os.path.exists(output_path):
                    return output_path
        except Exception as copy_error:
            logger.error(f"Error creating fallback copy: {str(copy_error)}")

        return None

def try_generate_with_fallback(template_path, output_path, placeholders, engagement=None):
    """
    Try to generate a document with placeholders, but fall back to a direct copy if that fails.
    This function will first try to use DocxTemplate to replace placeholders, but if that fails,
    it will fall back to a direct copy of the template.

    Args:
        template_path (str): Path to the template file
        output_path (str): Path to save the generated document
        placeholders (dict): Placeholders to replace in the template
        engagement: Optional engagement object for section removal

    Returns:
        str: Path to the generated document, or None if generation failed
    """
    # First try to generate the document with placeholders
    logger.info(f"Trying to generate document with placeholders from {template_path} to {output_path}")
    try:
        # Resolve the template path for bundled environments
        resolved_template_path = resolve_template_path(template_path)
        logger.info(f"Resolved template path: {resolved_template_path}")

        # Check if the template exists
        if not os.path.exists(resolved_template_path):
            logger.error(f"Template not found at resolved path: {resolved_template_path}")

            # List all available templates for debugging
            logger.info("Available templates:")
            for template_file in get_all_template_files():
                logger.info(f"  - {template_file}")

            return None

        # Create the output directory if it doesn't exist
        os.makedirs(os.path.dirname(output_path), exist_ok=True)

        # Load the template
        logger.info(f"Loading template: {resolved_template_path}")
        doc = DocxTemplate(resolved_template_path)

        # Create a custom Jinja2 environment to handle ampersands correctly
        logger.info("Creating custom Jinja2 environment")
        custom_jinja_env = jinja2.Environment(autoescape=False)
        doc.jinja_env = custom_jinja_env

        # Process placeholders to handle ampersands and special cases
        processed_placeholders = {}
        for key, value in placeholders.items():
            # Handle RichText objects
            if hasattr(value, '__class__') and value.__class__.__name__ == 'RichText':
                # Pass RichText objects through as-is
                processed_placeholders[key] = value
                logger.info(f"Preserving RichText object for {key}")
                continue

            # Convert to string if not already
            if value is None:
                processed_value = ""
            elif not isinstance(value, str):
                processed_value = str(value)
            else:
                processed_value = value

            # Decode HTML entities if present
            if isinstance(processed_value, str):
                # Decode HTML entities
                processed_value = html.unescape(processed_value)
                # Handle specific HTML entities that might not be caught
                entity_map = {
                    '&#x27;': "'",  # Apostrophe
                    '&apos;': "'",  # Apostrophe
                    '&quot;': '"',  # Double quote
                    '&amp;': '&',   # Ampersand
                    '&lt;': '<',    # Less than
                    '&gt;': '>',    # Greater than
                    '&#39;': "'",  # Apostrophe (decimal)
                    '&#34;': '"',  # Double quote (decimal)
                    '&#38;': '&',   # Ampersand (decimal)
                    '&#60;': '<',   # Less than (decimal)
                    '&#62;': '>',   # Greater than (decimal)
                }
                for entity, char in entity_map.items():
                    processed_value = processed_value.replace(entity, char)

                # Remove any XML tags that might be in the value
                processed_value = re.sub(r'<[^>]+>', '', processed_value)

                # Special handling for law firm names with ampersands
                if '&' in processed_value and key in ['law_firm', 'lawfirm', 'Lawfirm', 'firm', 'counsel']:
                    from docxtpl import RichText
                    rt = RichText()
                    rt.add(processed_value)
                    processed_placeholders[key] = rt
                    logger.info(f"Converting string with ampersand to RichText for {key}: {processed_value}")
                    continue

            processed_placeholders[key] = processed_value

        # Replace placeholders
        logger.info("Rendering template with placeholders")
        doc.render(processed_placeholders)

        # Save the document
        logger.info(f"Saving document to: {output_path}")
        doc.save(output_path)
        # Post-process: remove any marked sections based on engagement selection
        try:
            if engagement is not None and hasattr(engagement, 'get_sections_to_remove'):
                to_remove = engagement.get_sections_to_remove()
                if to_remove:
                    remove_marked_sections(output_path, to_remove)
        except Exception as e:
            logger.error(f"Section removal step failed: {e}")

        # Verify the document was created
        if os.path.exists(output_path):
            logger.info(f"Document generated successfully with placeholders: {output_path}")
            return output_path
    except Exception as e:
        logger.error(f"Error generating document with placeholders: {str(e)}")
        logger.error(traceback.format_exc())

    # If we get here, the document generation with placeholders failed
    # Fall back to a direct copy
    logger.info(f"Falling back to direct copy for {resolved_template_path} to {output_path}")
    try:
        # Create the output directory if it doesn't exist
        os.makedirs(os.path.dirname(output_path), exist_ok=True)

        # Copy the template to the output path
        shutil.copy2(resolved_template_path, output_path)
        logger.info(f"Copied template {resolved_template_path} to {output_path}")

        # Verify the document was created
        if os.path.exists(output_path):
            logger.info(f"Document created successfully with direct copy: {output_path}")
            return output_path
        else:
            logger.error(f"Failed to create document with direct copy: {output_path}")
            return None
    except Exception as e:
        logger.error(f"Error creating document with direct copy: {str(e)}")
        logger.error(traceback.format_exc())
        return None


def remove_marked_sections(docx_path: str, sections_to_remove: list[str]) -> bool:
    """
    Remove sections delimited by HTML-like markers from a generated DOCX.

    Markers in the template should be plain text paragraphs:
      <!-- BEGIN section_name -->
      ... content ...
      <!-- END section_name -->

    Only sections whose names appear in sections_to_remove will be removed.

    Special handling: If "phase5_pricing_section" is in sections_to_remove,
    also removes Phase 5 pricing lines and excess whitespace.

    Returns True if any changes were made.
    """
    try:
        if not sections_to_remove:
            return False
        doc = Document(docx_path)
        text_runs = []
        # Build a flat list of (paragraph, index) for safe editing
        for i, p in enumerate(doc.paragraphs):
            text_runs.append((i, p.text))
        # Find ranges to delete
        ranges = []
        for sec in sections_to_remove:
            start_idx = end_idx = None
            begin_pat = f"<!--begin{sec.lower()}-->"
            end_pat = f"<!--end{sec.lower()}-->"

            for idx, txt in text_runs:
                norm = re.sub(r"\s+", "", (txt or "")).lower()

                # Check for BEGIN marker
                if start_idx is None and begin_pat in norm:
                    start_idx = idx
                    # If this same paragraph also contains the END marker, use it
                    if end_pat in norm:
                        end_idx = idx
                        break
                # Check for END marker (only if we already found BEGIN)
                elif start_idx is not None and end_pat in norm:
                    end_idx = idx
                    break
            if start_idx is not None and end_idx is not None and end_idx >= start_idx:
                ranges.append((start_idx, end_idx))
        # Also remove any block items (including tables) between marker paragraphs in the document body
        body_removal_success = False
        try:
            body = doc.element.body
            body_children = list(body.iterchildren())
            for start_idx, end_idx in sorted(ranges, reverse=True):
                # Check bounds before accessing paragraphs
                if start_idx >= len(doc.paragraphs) or end_idx >= len(doc.paragraphs):
                    continue

                # Locate the begin/end paragraph elements in the body sequence
                begin_elem = doc.paragraphs[start_idx]._element
                end_elem = doc.paragraphs[end_idx]._element
                i_begin = i_end = None
                for i, child in enumerate(body_children):
                    if child is begin_elem:
                        i_begin = i
                    if child is end_elem:
                        i_end = i
                    if i_begin is not None and i_end is not None:
                        break
                if i_begin is not None and i_end is not None:
                    # Remove all block elements between and including begin/end
                    for i in range(i_end, i_begin - 1, -1):
                        body.remove(body_children[i])
                    body_removal_success = True
        except Exception:
            # Fall back to paragraph-only removal if body traversal fails
            pass

        # Only use fallback paragraph removal if body removal didn't work
        if not body_removal_success:
            # Delete paragraphs from bottom to top to preserve indices (safe fallback)
            for start_idx, end_idx in sorted(ranges, reverse=True):
                for idx in range(end_idx, start_idx - 1, -1):
                    # Check bounds before accessing paragraphs
                    if idx < len(doc.paragraphs):
                        p = doc.paragraphs[idx]._element
                        if p.getparent() is not None:
                            p.getparent().remove(p)
        # Always strip leftover marker lines (for kept sections too)
        cleaned_any = False
        for p in list(doc.paragraphs):
            raw = p.text or ""
            norm = re.sub(r"\s+", "", raw).lower()
            if "<!--begin" in norm or "<!--end" in norm:
                # If the paragraph is only a marker (ignoring whitespace), remove it
                only_marker = re.fullmatch(r"\s*<!--\s*(BEGIN|END)[^>]*-->\s*", raw, flags=re.IGNORECASE) is not None
                if only_marker:
                    elem = p._element
                    elem.getparent().remove(elem)
                else:
                    # Remove marker tokens inline but keep the rest of the paragraph
                    new_raw = re.sub(r"<!--\s*BEGIN[^>]*-->\s*", "", raw, flags=re.IGNORECASE)
                    new_raw = re.sub(r"<!--\s*END[^>]*-->\s*", "", new_raw, flags=re.IGNORECASE)
                    if new_raw != raw:
                        p.text = new_raw
                cleaned_any = True
        # Now process tables: look for markers inside table cell paragraphs
        table_changed = False
        for tbl in doc.tables:
            # Flatten paragraphs in row-major order
            tbl_paras = []
            for row in tbl.rows:
                for cell in row.cells:
                    for p in cell.paragraphs:
                        tbl_paras.append(p)
            # Find and collect ranges inside this table
            ranges_tbl = []
            for sec in sections_to_remove:
                start_idx = end_idx = None
                begin_pat = f"<!--begin{sec.lower()}-->"
                end_pat = f"<!--end{sec.lower()}-->"
                for idx, p in enumerate(tbl_paras):
                    norm = re.sub(r"\s+", "", (p.text or "")).lower()
                    if start_idx is None and begin_pat in norm:
                        start_idx = idx
                    elif start_idx is not None and end_pat in norm:
                        end_idx = idx
                        break
                if start_idx is not None and end_idx is not None and end_idx >= start_idx:
                    ranges_tbl.append((start_idx, end_idx))
            # Delete ranges inside the table
            for start_idx, end_idx in sorted(ranges_tbl, reverse=True):
                for idx in range(end_idx, start_idx - 1, -1):
                    elem = tbl_paras[idx]._element
                    elem.getparent().remove(elem)
                    table_changed = True
            # Strip leftover marker lines or inline markers within table paragraphs
            for row in tbl.rows:
                for cell in row.cells:
                    for p in list(cell.paragraphs):
                        raw = p.text or ""
                        norm = re.sub(r"\s+", "", raw).lower()
                        if "<!--begin" in norm or "<!--end" in norm:
                            only_marker = re.fullmatch(r"\s*<!--\s*(BEGIN|END)[^>]*-->\s*", raw, flags=re.IGNORECASE) is not None
                            if only_marker:
                                pe = p._element
                                pe.getparent().remove(pe)
                                table_changed = True
                            else:
                                new_raw = re.sub(r"<!--\s*BEGIN[^>]*-->\s*", "", raw, flags=re.IGNORECASE)
                                new_raw = re.sub(r"<!--\s*END[^>]*-->\s*", "", new_raw, flags=re.IGNORECASE)
                                if new_raw != raw:
                                    p.text = new_raw
                                    table_changed = True
        # Special handling for Phase 5/CS210 pricing removal
        pricing_removed = False
        if ("phase5_pricing_section" in sections_to_remove) or ("cs210_pricing_section" in sections_to_remove):
            pricing_removed = _remove_phase5_pricing(doc)

        if ranges or cleaned_any or table_changed or pricing_removed:
            doc.save(docx_path)
            logger.info(f"Removed sections: {sections_to_remove}")
            return True
        return False
    except Exception as e:
        logger.error(f"Error removing marked sections: {e}")
        logger.error(traceback.format_exc())
        return False


def _remove_phase5_pricing(doc) -> bool:
    """
    Remove Phase 5/CS210 pricing lines and excess whitespace.

    Strategy:
    - Identify the Phase 5/CS210 section bounds by finding a heading line that
      contains either "Phase 5"+"Endpoint" or "CS210" and stopping at the next
      phase heading ("Phase 6", "CS410", or "CS430").
    - Within those bounds, remove any paragraphs that contain "Estimated Hours"
      or "Estimated Costs". Also trim the following empty paragraphs to reduce
      whitespace.

    Returns True if any changes were made.
    """
    try:
        paragraphs_to_remove = []
        paras = doc.paragraphs
        n = len(paras)

        # 1) Find start of Phase 5/CS210 section
        start_idx = None
        end_idx = None
        for i, p in enumerate(paras):
            t = (p.text or "").strip()
            if ("Phase 5" in t and "Endpoint" in t) or ("CS210" in t and "Endpoint" in t):
                start_idx = i
                break
        if start_idx is None:
            return False

        # 2) Find end of section
        for j in range(start_idx + 1, n):
            t = (paras[j].text or "").strip()
            if ("Phase 6" in t) or ("CS410" in t) or ("CS430" in t):
                end_idx = j
                break
        if end_idx is None:
            end_idx = n

        # 3) Scan within bounds for pricing lines and mark them for removal
        k = start_idx + 1
        while k < end_idx:
            t = (paras[k].text or "").strip()
            if ("Estimated Hours" in t) or ("Estimated Costs" in t):
                paragraphs_to_remove.append(paras[k])
                # Also mark immediate trailing empty paragraphs
                k2 = k + 1
                while k2 < min(k + 4, end_idx):
                    nt = (paras[k2].text or "").strip()
                    if nt == "":
                        paragraphs_to_remove.append(paras[k2])
                        k2 += 1
                    else:
                        break
                # Continue search in case there are multiple pricing lines separated
                k = k2
                continue
            k += 1

        # 4) Remove marked paragraphs
        for para in paragraphs_to_remove:
            el = para._element
            if el.getparent() is not None:
                el.getparent().remove(el)

        return len(paragraphs_to_remove) > 0

    except Exception as e:
        logger.error(f"Error removing Phase 5 pricing: {e}")
        return False



class ReliableDocumentGenerator:
    """
    Class for generating documents for engagements using a reliable approach.
    """

    def __init__(self, engagement, output_dir=None):
        """
        Initialize a new ReliableDocumentGenerator object.

        Args:
            engagement: The engagement object
            output_dir (str): The directory where documents should be saved
        """
        self.engagement = engagement

        # Set the output directory
        if output_dir:
            # Use the exact directory the user selected
            self.output_dir = output_dir
        else:
            # If no directory provided, create one in the current working directory
            self.output_dir = os.path.join(os.getcwd(), self.engagement.get_document_folder_name())

        # Create the output directory if it doesn't exist
        os.makedirs(self.output_dir, exist_ok=True)

        # Log the actual output directory being used
        logger.info(f"Using output directory: {self.output_dir}")

        logger.info(f"ReliableDocumentGenerator initialized with output directory: {self.output_dir}")

        # Debug template paths
        self._debug_template_paths()

    def _generate_document_with_fallback(self, template_path, output_path, placeholders, doc_type):
        """
        Centralized document generation with fallback logic.

        Args:
            template_path (str): Path to the template file
            output_path (str): Path to save the generated document
            placeholders (dict): Placeholders to replace in the template
            doc_type (str): Type of document for logging purposes

        Returns:
            str: Path to the generated document, or None if generation failed
        """
        # Try to use python-docx to replace placeholders first
        try:
            logger.info(f"Using python-docx to replace placeholders in {doc_type} document")
            result = replace_placeholders_in_docx(template_path, output_path, placeholders)
    
            if result:
                # After placeholder replacement, remove any marked sections based on engagement selection
                try:
                    sections = getattr(self, 'engagement', None)
                    if sections and hasattr(self.engagement, 'get_sections_to_remove'):
                        to_remove = self.engagement.get_sections_to_remove()
                        if to_remove:
                            remove_marked_sections(output_path, to_remove)
                except Exception as e:
                    logger.error(f"Section removal step failed: {e}")
                logger.info(f"{doc_type} document created successfully with python-docx: {result}")
                return result
        except Exception as e:
            logger.error(f"Error using python-docx to replace placeholders: {e}")
            logger.error(traceback.format_exc())

        # If python-docx fails, fall back to generate_document_reliable
        logger.info(f"Falling back to generate_document_reliable for {doc_type} document")
        return generate_document_reliable(template_path, output_path, placeholders, self.engagement)

    def generate_documents(self):
        """
        Generate all documents for the engagement.

        Returns:
            list: List of paths to the generated documents
        """
        logger.info(f"Generating documents for {self.engagement.engagement_type} engagement")
        logger.info(f"Client: {self.engagement.client.name}")
        logger.info(f"Law Firm: {self.engagement.client.law_firm}")
        logger.info(f"Output directory: {self.output_dir}")

        generated_documents = []

        # Generate SOW
        sow_path = self.generate_sow()
        if sow_path:
            generated_documents.append(sow_path)

        # Check if TACI is needed
        if hasattr(self.engagement, 'include_taci') and self.engagement.include_taci:
            logger.info("TACI SOW is needed")
            taci_path = self.generate_taci_sow()
            if taci_path:
                generated_documents.append(taci_path)

        # Check if RR SOW document should be generated
        should_generate_rr = (
            self.engagement.engagement_type == "RR" or
            (hasattr(self.engagement, 'generate_rr_sow') and self.engagement.generate_rr_sow) or
            (hasattr(self.engagement, 'include_rr') and self.engagement.include_rr)
        )

        if should_generate_rr:
            logger.info("RR SOW document generation requested (RR engagement, explicit flag, or DFIR include_rr)")
            rr_path = self.generate_rr_sow()
            if rr_path:
                generated_documents.append(rr_path)
        else:
            logger.info("RR SOW document generation not requested")

        # Generate MSA if needed
        if hasattr(self.engagement, 'needs_msa') and self.engagement.needs_msa:
            logger.info("MSA is needed")
            msa_path = self.generate_msa()
            if msa_path:
                generated_documents.append(msa_path)
        else:
            logger.info("MSA is not needed")

        # Generate BAA if needed
        if hasattr(self.engagement.client, 'needs_baa') and self.engagement.client.needs_baa:
            logger.info("BAA is needed")
            baa_path = self.generate_baa()
            if baa_path:
                generated_documents.append(baa_path)

        # Generate DPA if needed
        if hasattr(self.engagement.client, 'needs_dpa') and self.engagement.client.needs_dpa:
            logger.info("DPA is needed")
            dpa_path = self.generate_dpa()
            if dpa_path:
                generated_documents.append(dpa_path)

        # Log the generated documents
        logger.info(f"Generated {len(generated_documents)} documents:")
        for i, doc in enumerate(generated_documents):
            logger.info(f"Document {i+1}: {doc}")

        return generated_documents

    def generate_sow(self):
        """
        Generate the SOW document.

        Returns:
            str: Path to the generated SOW document
        """
        logger.info("Generating SOW...")

        # Get the template path
        template_path = self.engagement.get_sow_template_path()
        logger.info(f"SOW template path: {template_path}")

        # Get the output path
        output_path = os.path.join(self.output_dir, self.engagement.get_sow_filename())
        logger.info(f"SOW output path: {output_path}")

        # Get the placeholders
        placeholders = self.engagement.get_placeholders()

        # Use centralized document generation with fallback
        return self._generate_document_with_fallback(template_path, output_path, placeholders, "SOW")

    def generate_msa(self):
        """
        Generate the MSA document.

        Returns:
            str: Path to the generated MSA document
        """
        logger.info("Generating MSA...")

        # Get the template path
        template_path = self.engagement.get_msa_template_path()
        logger.info(f"MSA template path: {template_path}")

        # Get the output path
        output_path = os.path.join(self.output_dir, self.engagement.get_msa_filename())
        logger.info(f"MSA output path: {output_path}")

        # Get the placeholders
        placeholders = self.engagement.get_placeholders()

        # Try to use python-docx to replace placeholders
        try:
            logger.info(f"Using python-docx to replace placeholders in MSA document")
            result = replace_placeholders_in_docx(template_path, output_path, placeholders)
            if result:
                logger.info(f"MSA document created successfully with python-docx: {result}")
                return result
        except Exception as e:
            logger.error(f"Error using python-docx to replace placeholders: {e}")
            logger.error(traceback.format_exc())

        # If python-docx fails, fall back to direct copy
        try:
            # Create the output directory if it doesn't exist
            os.makedirs(os.path.dirname(output_path), exist_ok=True)

            # Copy the template to the output path
            logger.info(f"Falling back to direct copy for MSA document")
            shutil.copy2(template_path, output_path)
            logger.info(f"Copied template {template_path} to {output_path}")

            # Verify the document was created
            if os.path.exists(output_path):
                logger.info(f"MSA document created successfully with direct copy: {output_path}")
                return output_path
            else:
                logger.error(f"Failed to create MSA document: {output_path}")
                return None
        except Exception as e:
            logger.error(f"Error creating MSA document: {e}")
            logger.error(traceback.format_exc())
            return None

    def generate_baa(self):
        """
        Generate the BAA document.

        Returns:
            str: Path to the generated BAA document
        """
        logger.info("Generating BAA...")

        # Get the template path
        template_path = self.engagement.get_baa_template_path()
        logger.info(f"BAA template path: {template_path}")

        # Get the output path
        output_path = os.path.join(self.output_dir, self.engagement.get_baa_filename())
        logger.info(f"BAA output path: {output_path}")

        # Get the placeholders
        placeholders = self.engagement.get_placeholders()

        # Generate the document
        return generate_document_reliable(template_path, output_path, placeholders, self.engagement)

    def generate_dpa(self):
        """
        Generate the DPA document.

        Returns:
            str: Path to the generated DPA document
        """
        logger.info("Generating DPA...")

        # Get the template path
        template_path = self.engagement.get_dpa_template_path()
        logger.info(f"DPA template path: {template_path}")

        # Get the output path
        output_path = os.path.join(self.output_dir, self.engagement.get_dpa_filename())
        logger.info(f"DPA output path: {output_path}")

        # Get the placeholders
        placeholders = self.engagement.get_placeholders()

        # Try to use python-docx to replace placeholders
        try:
            logger.info(f"Using python-docx to replace placeholders in DPA document")
            result = replace_placeholders_in_docx(template_path, output_path, placeholders)
            if result:
                logger.info(f"DPA document created successfully with python-docx: {result}")
                return result
        except Exception as e:
            logger.error(f"Error using python-docx to replace placeholders: {e}")
            logger.error(traceback.format_exc())

        # If python-docx fails, fall back to direct copy
        try:
            # Create the output directory if it doesn't exist
            os.makedirs(os.path.dirname(output_path), exist_ok=True)

            # Copy the template to the output path
            logger.info(f"Falling back to direct copy for DPA document")
            shutil.copy2(template_path, output_path)
            logger.info(f"Copied template {template_path} to {output_path}")

            # Verify the document was created
            if os.path.exists(output_path):
                logger.info(f"DPA document created successfully with direct copy: {output_path}")
                return output_path
            else:
                logger.error(f"Failed to create DPA document: {output_path}")
                return None
        except Exception as e:
            logger.error(f"Error creating DPA document: {e}")
            logger.error(traceback.format_exc())
            return None

    def generate_taci_sow(self):
        """
        Generate the TACI SOW document.

        Returns:
            str: Path to the generated TACI SOW document
        """
        logger.info("Generating TACI SOW...")

        # Get the template path
        template_path = self.engagement.get_taci_sow_template_path()
        logger.info(f"TACI SOW template path: {template_path}")

        # Get the output path
        output_path = os.path.join(self.output_dir, self.engagement.get_taci_sow_filename())
        logger.info(f"TACI SOW output path: {output_path}")

        # Get the placeholders
        placeholders = self.engagement.get_placeholders()

        # Use centralized document generation with fallback
        return self._generate_document_with_fallback(template_path, output_path, placeholders, "TACI SOW")

    def generate_rr_sow(self):
        """
        Generate the RR SOW document.

        Returns:
            str: Path to the generated RR SOW document
        """
        logger.info("Generating RR SOW...")

        # Get the template path
        template_path = self.engagement.get_rr_sow_template_path()
        logger.info(f"RR SOW template path: {template_path}")

        # Get the output path
        output_path = os.path.join(self.output_dir, self.engagement.get_rr_sow_filename())
        logger.info(f"RR SOW output path: {output_path}")

        # Get the placeholders
        placeholders = self.engagement.get_placeholders()

        # Use centralized document generation with fallback
        return self._generate_document_with_fallback(template_path, output_path, placeholders, "RR SOW")

    def _debug_template_paths(self):
        """
        Debug method to check all template paths for the engagement.
        This helps diagnose issues with template path resolution.
        """
        logger.info("Debugging template paths...")

        # Check SOW template path
        sow_template_path = self.engagement.get_sow_template_path()
        resolved_sow_path = resolve_template_path(sow_template_path)
        logger.info(f"SOW template path: {sow_template_path}")
        logger.info(f"Resolved SOW template path: {resolved_sow_path}")
        logger.info(f"SOW template exists: {os.path.exists(resolved_sow_path)}")

        # Check MSA template path
        msa_template_path = self.engagement.get_msa_template_path()
        resolved_msa_path = resolve_template_path(msa_template_path)
        logger.info(f"MSA template path: {msa_template_path}")
        logger.info(f"Resolved MSA template path: {resolved_msa_path}")
        logger.info(f"MSA template exists: {os.path.exists(resolved_msa_path)}")

        # Check if TACI is needed
        if hasattr(self.engagement, 'include_taci') and self.engagement.include_taci:
            taci_template_path = self.engagement.get_taci_sow_template_path()
            resolved_taci_path = resolve_template_path(taci_template_path)
            logger.info(f"TACI SOW template path: {taci_template_path}")
            logger.info(f"Resolved TACI SOW template path: {resolved_taci_path}")
            logger.info(f"TACI SOW template exists: {os.path.exists(resolved_taci_path)}")

        # Check if RR is needed
        if self.engagement.engagement_type == "RR" or (hasattr(self.engagement, 'include_rr') and self.engagement.include_rr):
            rr_template_path = self.engagement.get_rr_sow_template_path()
            resolved_rr_path = resolve_template_path(rr_template_path)
            logger.info(f"RR SOW template path: {rr_template_path}")
            logger.info(f"Resolved RR SOW template path: {resolved_rr_path}")
            logger.info(f"RR SOW template exists: {os.path.exists(resolved_rr_path)}")

        # Check if BAA is needed
        if hasattr(self.engagement.client, 'needs_baa') and self.engagement.client.needs_baa:
            baa_template_path = self.engagement.get_baa_template_path()
            resolved_baa_path = resolve_template_path(baa_template_path)
            logger.info(f"BAA template path: {baa_template_path}")
            logger.info(f"Resolved BAA template path: {resolved_baa_path}")
            logger.info(f"BAA template exists: {os.path.exists(resolved_baa_path)}")

        # Check if DPA is needed
        if hasattr(self.engagement.client, 'needs_dpa') and self.engagement.client.needs_dpa:
            dpa_template_path = self.engagement.get_dpa_template_path()
            resolved_dpa_path = resolve_template_path(dpa_template_path)
            logger.info(f"DPA template path: {dpa_template_path}")
            logger.info(f"Resolved DPA template path: {resolved_dpa_path}")
            logger.info(f"DPA template exists: {os.path.exists(resolved_dpa_path)}")

        # List all available templates
        logger.info("All available templates:")
        for template_file in get_all_template_files():
            logger.info(f"  - {template_file}")
