"""
Placeholder audit utility for PACE.
- Scans all .docx templates for Jinja-style placeholders {{ ... }}
- Generates placeholders from sample DFIR, BEC, RR engagements
- Compares and reports: matched vs missing per template

Run:
  python utils/placeholder_audit.py

Outputs:
  - Prints a summary to console
  - Writes a detailed report to audit/placeholders_report.txt
"""

import os
import re
import zipfile
from pathlib import Path
from datetime import datetime

# Import models
import sys
# Ensure project root is on sys.path when running directly
ROOT = Path(__file__).resolve().parents[1]
if str(ROOT) not in sys.path:
    sys.path.insert(0, str(ROOT))

from models.client import Client
from models.dfir import DFIREngagement
try:
    from models.bec import BECEngagement
except Exception:
    BECEngagement = None
try:
    from models.rr import RREngagement
except Exception:
    RREngagement = None

ROOT = Path(__file__).resolve().parents[1]
TEMPLATES_DIR = ROOT / "templates"
REPORT_DIR = ROOT / "audit"
REPORT_DIR.mkdir(exist_ok=True)
REPORT_PATH = REPORT_DIR / "placeholders_report.txt"

PLACEHOLDER_RE = re.compile(r"\{\{\s*([^}]+?)\s*\}\}")


def extract_placeholders_from_docx(docx_path: Path) -> set[str]:
    placeholders: set[str] = set()
    try:
        with zipfile.ZipFile(docx_path, 'r') as z:
            # Scan document, headers, footers
            members = [
                name for name in z.namelist()
                if name.startswith('word/') and (
                    name.endswith('.xml') and (
                        'document' in name or 'header' in name or 'footer' in name or 'footnotes' in name or 'endnotes' in name
                    )
                )
            ]
            for name in members:
                try:
                    data = z.read(name).decode('utf-8', errors='ignore')
                except Exception:
                    continue
                # Find {{ ... }} occurrences
                for m in PLACEHOLDER_RE.finditer(data):
                    raw = m.group(1)
                    # Normalize: strip filters and whitespace
                    # e.g. var|upper -> var; support dotted names as-is
                    var = raw.split('|', 1)[0].strip()
                    # Some templates may include braces artifacts; keep clean
                    if var:
                        placeholders.add(var)
    except zipfile.BadZipFile:
        pass
    return placeholders


def sample_placeholders_for_dfir() -> dict:
    c = Client(name="AuditCo", insurance_carrier="Beazley", law_firm="Baker & Hostetler LLP")
    e = DFIREngagement(c)
    e.endpoint_count = 150
    e.rr_is_remote = True
    e.edr_monitoring = "Monitoring and Threat Hunting New Console"
    e.include_taci = False
    e.include_rr = False
    return e.get_placeholders()


def sample_placeholders_for_bec() -> dict:
    if BECEngagement is None:
        return {}
    c = Client(name="AuditCo", insurance_carrier="Beazley", law_firm="Baker & Hostetler LLP")
    e = BECEngagement(c)
    e.email_platform = "M365"
    e.tenant_size = 1000
    e.mailbox_count = 10
    e.include_message_extraction = True
    e.message_extraction_cost = 1500
    e.include_triage_analysis = False
    e.triage_count = 0
    e.is_e5_license = False
    return e.get_placeholders()


def sample_placeholders_for_rr() -> dict:
    if RREngagement is None:
        return {}
    c = Client(name="AuditCo", insurance_carrier="Beazley", law_firm="Baker & Hostetler LLP")
    e = RREngagement(c)
    e.is_remote = True
    e.onsite_resources_count = 1
    e.resource_count = 1
    e.include_decryption = False
    # RR model may not implement get_placeholders; handle gracefully
    return getattr(e, 'get_placeholders', lambda: {})()


def gather_templates() -> list[Path]:
    return list(TEMPLATES_DIR.rglob("*.docx"))


def audit_templates():
    dfir_ctx = sample_placeholders_for_dfir()
    bec_ctx = sample_placeholders_for_bec()
    rr_ctx = sample_placeholders_for_rr()

    def context_for_path(p: Path) -> dict:
        # Heuristic: choose engagement context based on path segments
        low = str(p).lower()
        if "\\dfir\\" in low or "/dfir/" in low:
            return dfir_ctx
        if "\\bec\\" in low or "/bec/" in low:
            return bec_ctx
        if "\\rr\\" in low or "/rr/" in low:
            return rr_ctx
        # Default to DFIR
        return dfir_ctx

    lines = []
    lines.append(f"PACE Placeholder Audit - {datetime.now().isoformat(timespec='seconds')}\n")

    templates = gather_templates()
    total_missing = 0
    total_placeholders = 0

    for t in sorted(templates, key=lambda p: str(p)):
        ph = extract_placeholders_from_docx(t)
        total_placeholders += len(ph)
        ctx = context_for_path(t)
        missing = sorted([k for k in ph if k not in ctx])
        matched = sorted([k for k in ph if k in ctx])
        total_missing += len(missing)

        lines.append(f"Template: {t.relative_to(ROOT)}")
        lines.append(f"  Placeholders found: {len(ph)}  Matched: {len(matched)}  Missing: {len(missing)}")
        if missing:
            lines.append("  Missing:")
            for k in missing:
                lines.append(f"    - {k}")
        if matched:
            lines.append("  Matched (sample values):")
            # Show a few sample values for matched ones
            show = matched[:10]
            for k in show:
                v = ctx.get(k)
                lines.append(f"    - {k} = {repr(v)[:120]}")
        lines.append("")

    lines.append(f"Summary: {len(templates)} templates scanned, {total_placeholders} placeholders total, {total_missing} missing across all.")

    REPORT_PATH.write_text("\n".join(lines), encoding='utf-8')
    print(REPORT_PATH.read_text(encoding='utf-8'))


if __name__ == "__main__":
    audit_templates()

