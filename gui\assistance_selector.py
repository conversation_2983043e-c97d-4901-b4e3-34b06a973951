"""
Custom widget for selecting assistance type (Remote or Onsite).
"""

from PySide6.QtWidgets import QWidget, QHBoxLayout, QPushButton, QSpinBox, QLabel
from PySide6.QtCore import Signal, Qt

class AssistanceSelector(QWidget):
    """
    Custom widget for selecting assistance type (Remote or Onsite).
    """
    
    # Signal emitted when the assistance type changes
    assistance_changed = Signal(bool, int)  # is_remote, resource_count
    
    def __init__(self, parent=None):
        """
        Initialize the assistance selector.
        
        Args:
            parent: The parent widget
        """
        super().__init__(parent)
        
        # Set up the layout
        self.layout = QHBoxLayout(self)
        self.layout.setContentsMargins(0, 0, 0, 0)
        
        # Create the buttons
        self.remote_button = QPushButton("Remote Assistance")
        self.onsite_button = QPushButton("Onsite Assistance")
        
        # Style the buttons
        self.remote_button.setCheckable(True)
        self.onsite_button.setCheckable(True)
        
        # Set the initial state
        self.remote_button.setChecked(True)
        self.is_remote = True
        
        # Create the resource count widgets
        self.resources_label = QLabel("Number of Resources:")
        self.resources_spin = QSpinBox()
        self.resources_spin.setRange(1, 10)
        self.resources_spin.setValue(1)
        self.resources_spin.setEnabled(False)  # Disabled by default for remote
        
        # Add the widgets to the layout
        self.layout.addWidget(self.remote_button)
        self.layout.addWidget(self.onsite_button)
        self.layout.addWidget(self.resources_label)
        self.layout.addWidget(self.resources_spin)
        
        # Connect signals
        self.remote_button.clicked.connect(self.on_remote_clicked)
        self.onsite_button.clicked.connect(self.on_onsite_clicked)
        self.resources_spin.valueChanged.connect(self.emit_change)
    
    def on_remote_clicked(self):
        """
        Handle the Remote Assistance button click.
        """
        print("Remote button clicked in custom widget")
        # Update button states
        self.remote_button.setChecked(True)
        self.onsite_button.setChecked(False)
        
        # Disable and reset the resource count
        self.resources_spin.setValue(1)
        self.resources_spin.setEnabled(False)
        
        # Update the state
        self.is_remote = True
        
        # Emit the signal
        self.emit_change()
    
    def on_onsite_clicked(self):
        """
        Handle the Onsite Assistance button click.
        """
        print("Onsite button clicked in custom widget")
        # Update button states
        self.remote_button.setChecked(False)
        self.onsite_button.setChecked(True)
        
        # Enable the resource count
        self.resources_spin.setEnabled(True)
        
        # Update the state
        self.is_remote = False
        
        # Emit the signal
        self.emit_change()
    
    def emit_change(self):
        """
        Emit the assistance_changed signal.
        """
        self.assistance_changed.emit(self.is_remote, self.resources_spin.value())
    
    def get_values(self):
        """
        Get the current values.
        
        Returns:
            tuple: (is_remote, resource_count)
        """
        return (self.is_remote, self.resources_spin.value())
    
    def set_values(self, is_remote, resource_count=1):
        """
        Set the values.
        
        Args:
            is_remote (bool): Whether the assistance is remote
            resource_count (int): The number of resources
        """
        # Update the state
        self.is_remote = is_remote
        
        # Update the UI
        if is_remote:
            self.remote_button.setChecked(True)
            self.onsite_button.setChecked(False)
            self.resources_spin.setValue(1)
            self.resources_spin.setEnabled(False)
        else:
            self.remote_button.setChecked(False)
            self.onsite_button.setChecked(True)
            self.resources_spin.setValue(resource_count)
            self.resources_spin.setEnabled(True)
