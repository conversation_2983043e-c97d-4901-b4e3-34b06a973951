"""
Custom DPA generator for PACE application.
This module provides a specialized approach to DPA document generation.
"""

import os
import sys
import shutil
import logging
from docxtpl import DocxTemplate
import jinja2

# Set up logging to user's temp directory to avoid permission issues
import tempfile
log_file = os.path.join(tempfile.gettempdir(), "pace_custom_dpa_generation.log")
logging.basicConfig(level=logging.INFO,  # Changed to INFO to reduce log size
                    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
                    handlers=[
                        logging.FileHandler(log_file),
                        logging.StreamHandler()
                    ])
logger = logging.getLogger(__name__)

def generate_dpa_document(output_dir, filename, placeholders):
    """
    Generate a DPA document using a specialized approach.
    
    Args:
        output_dir (str): Directory to save the generated document
        filename (str): Filename for the generated document
        placeholders (dict): Placeholders to replace in the template
        
    Returns:
        str: Path to the generated document, or None if generation failed
    """
    try:
        # Log the function call
        logger.info(f"Generating DPA document to {output_dir}/{filename}")
        
        # Use the hardcoded template path that we know works
        template_path = "templates/base_templates/dpa/Template_Data Processing Agreement (DPA).docx"
        logger.info(f"DPA template path (hardcoded): {template_path}")
        
        # Check if the template exists
        if not os.path.exists(template_path):
            logger.error(f"DPA template not found at hardcoded path: {template_path}")
            return None
            
        # Define the output path
        output_path = os.path.join(output_dir, filename)
        logger.info(f"DPA output path: {output_path}")
        
        # Create the output directory if it doesn't exist
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        
        # Process placeholders to handle ampersands and special cases
        processed_placeholders = {}
        for key, value in placeholders.items():
            # Convert to string if not already
            if value is None:
                processed_value = ""
            elif not isinstance(value, str):
                processed_value = str(value)
            else:
                processed_value = value
                
            processed_placeholders[key] = processed_value
            
        # First try: Use DocxTemplate to generate the document
        try:
            # Load the template
            logger.info(f"Loading template: {template_path}")
            doc = DocxTemplate(template_path)
            
            # Create a custom Jinja2 environment to handle ampersands correctly
            logger.info("Creating custom Jinja2 environment")
            custom_jinja_env = jinja2.Environment(autoescape=False)
            doc.jinja_env = custom_jinja_env
            
            # Replace placeholders
            logger.info("Rendering template with placeholders")
            doc.render(processed_placeholders)
            
            # Save the document
            logger.info(f"Saving document to: {output_path}")
            doc.save(output_path)
            
            # Verify the document was created
            if os.path.exists(output_path):
                logger.info(f"Document generated successfully: {output_path}")
                
                # Verify the document structure
                try:
                    with open(output_path, 'rb') as f:
                        header = f.read(4)
                        if header.startswith(b'PK'):
                            logger.info(f"Verified document structure is valid: {output_path}")
                        else:
                            logger.warning(f"Document may have invalid structure: {output_path}")
                except Exception as verify_error:
                    logger.error(f"Error verifying document: {verify_error}")
                
                return output_path
            else:
                logger.error(f"Document generation failed: Output file not found at {output_path}")
        except Exception as e:
            logger.error(f"Error generating document with DocxTemplate: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())
        
        # Second try: Simple copy as a fallback
        try:
            logger.info(f"Attempting fallback: copying template to output")
            # If the template exists but can't be rendered, create a copy
            if os.path.exists(template_path):
                # Create the output directory if it doesn't exist
                os.makedirs(os.path.dirname(output_path), exist_ok=True)
                
                # Copy the template to the output path
                shutil.copy2(template_path, output_path)
                logger.info(f"Created a copy of the template at {output_path} as a fallback")
                
                # Verify the document was created successfully
                if os.path.exists(output_path):
                    return output_path
        except Exception as copy_error:
            logger.error(f"Error creating fallback copy: {str(copy_error)}")
        
        # Third try: Create a simple text file as a last resort
        try:
            logger.info(f"Attempting last resort: creating a simple text file")
            
            # Create a simple text file with the DPA content
            text_output_path = os.path.splitext(output_path)[0] + ".txt"
            
            # Get client information
            client_name = processed_placeholders.get("client_name", "[CLIENT NAME]")
            formatted_date = processed_placeholders.get("engagement_date", "[DATE]")
            
            # Create the content of the text file
            content = f"""DATA PROCESSING AGREEMENT

This Data Processing Agreement ("DPA") is entered into between {client_name} ("Client") and Arete Advisors LLC ("Arete") as of {formatted_date}.

1. DEFINITIONS

1.1 "Personal Data" means any information relating to an identified or identifiable natural person.

2. DATA PROCESSING

2.1 Arete will process Personal Data only in accordance with Client's documented instructions.

2.2 Arete will ensure that persons authorized to process the Personal Data have committed themselves to confidentiality.

3. SECURITY

3.1 Arete will implement appropriate technical and organizational measures to ensure a level of security appropriate to the risk.

4. TERM AND TERMINATION

4.1 This DPA shall remain in effect for the duration of the Services Agreement between the parties.

SIGNATURES

IN WITNESS WHEREOF, the parties have executed this DPA as of the date first written above.


{client_name}

By: ________________________

Name: ______________________

Title: _______________________


Arete Advisors LLC

By: ________________________

Name: ______________________

Title: _______________________
"""
            
            # Write the content to the file
            with open(text_output_path, 'w') as f:
                f.write(content)
            
            logger.info(f"Created a simple text file at {text_output_path} as a last resort")
            
            # Return the text file path
            return text_output_path
        except Exception as text_error:
            logger.error(f"Error creating simple text file: {str(text_error)}")
        
        return None
    except Exception as e:
        logger.error(f"Unexpected error in generate_dpa_document: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        return None
