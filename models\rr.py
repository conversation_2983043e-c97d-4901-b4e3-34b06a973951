"""
Recovery and Remediation (RR) engagement model for PACE application.
"""

from models.engagement import Engagement
from data.carriers import get_carrier_rate

class RREngagement(Engagement):
    """
    Class representing a Recovery and Remediation (RR) engagement in the PACE application.
    """

    def __init__(self, client=None):
        """
        Initialize a new RREngagement object.

        Args:
            client (Client): The client for this engagement
        """
        super().__init__(client, "RR")
        self.is_remote = True
        self.onsite_resources_count = 1
        self.resource_count = 1  # Keep both for backward compatibility
        self.include_decryption = False
        self.is_single_price = True  # Always use single price model (no ranges)

    def to_dict(self):
        """
        Convert the RR engagement data to a dictionary.

        Returns:
            dict: A dictionary containing the RR engagement data
        """
        data = super().to_dict()
        data.update({
            "is_remote": self.is_remote,
            "onsite_resources_count": self.onsite_resources_count,
            "include_decryption": self.include_decryption,
            "is_single_price": self.is_single_price
        })
        return data

    @classmethod
    def from_dict(cls, data, client=None):
        """
        Create an RREngagement object from a dictionary.

        Args:
            data (dict): A dictionary containing RR engagement data
            client (Client): The client for this engagement

        Returns:
            RREngagement: A new RREngagement object
        """
        engagement = super().from_dict(data, client)

        engagement.is_remote = data.get("is_remote", True)
        engagement.onsite_resources_count = data.get("onsite_resources_count", 1)
        engagement.include_decryption = data.get("include_decryption", False)
        engagement.is_single_price = data.get("is_single_price", True)  # Default to single price

        return engagement

    def is_valid(self):
        """
        Check if the RR engagement data is valid.

        Returns:
            bool: True if the RR engagement data is valid, False otherwise
        """
        if not super().is_valid():
            return False

        # Resource count must be at least 1
        if self.resource_count < 1:
            return False

        return True

    def get_document_folder_name(self):
        """
        Get the name of the folder where documents for this engagement should be saved.

        Returns:
            str: The folder name
        """
        if not self.client or not self.client.name:
            return "Unnamed Client - Scoping Documents"

        return f"{self.client.name} - Scoping Documents"

    def get_sow_template_path(self):
        """
        Get the path to the SOW template for this engagement.

        Returns:
            str: The path to the SOW template
        """
        # Special case for Beazley with onsite support
        if not self.is_remote and self.client and self.client.insurance_carrier == "Beazley":
            return "templates/base_templates/rr/SOW_ON_Template_Beazley_Only_Recovery_&_Restoration_Support.docx"

        # Standard template for all other cases (remote or onsite for non-Beazley)
        return "templates/base_templates/rr/SOW_ON_Template_Recovery_&_Restoration_Support.docx"

    def get_rr_sow_template_path(self):
        """
        Get the path to the SOW template for this engagement.
        This is kept for backward compatibility.

        Returns:
            str: The path to the SOW template
        """
        return self.get_sow_template_path()

    def get_msa_template_path(self):
        """
        Get the path to the MSA template for this engagement.

        Returns:
            str: The path to the MSA template
        """
        if self.client and self.client.law_firm:
            # Check for law firm-specific MSA templates
            # Special case for Baker & Hostetler LLP with TASB
            if self.client.law_firm == "Baker & Hostetler LLP" and hasattr(self.client, 'is_tasb') and self.client.is_tasb:
                return "templates/base_templates/msa_templates/Baker_&_Hostetler_LLP_Beazley_TASB_Master_Services_Agreement_Template.docx"
            # Special case for Jackson Lewis PC with BAA
            elif self.client.law_firm == "Jackson Lewis PC" and self.needs_baa:
                return "templates/base_templates/msa_templates/Jackson_Lewis_PC_with_BAA_Master_Services_Agreement_Template.docx"
            # Standard law firm templates
            elif self.client.law_firm == "Baker & Hostetler LLP":
                return "templates/base_templates/msa_templates/Baker_&_Hostetler_LLP_Master_Services_Agreement_Template.docx"
            elif self.client.law_firm == "Greenberg Traurig, LLP":
                return "templates/base_templates/msa_templates/Greenberg_Traurig_LLP_Master_Services_Agreement_Template.docx"
            elif self.client.law_firm == "Jackson Lewis PC":
                return "templates/base_templates/msa_templates/Jackson_Lewis_PC_Master_Services_Agreement_Template.docx"
            elif self.client.law_firm == "The Beckage Firm":
                return "templates/base_templates/msa_templates/The_Beckage_Firm_Master_Services_Agreement_Template.docx"

        # Default MSA template
        return "templates/base_templates/msa_templates/Master_Services_Agreement_Template.docx"

    def get_msa_filename(self):
        """
        Get the filename for the MSA document.

        Returns:
            str: The filename for the MSA document
        """
        from datetime import datetime

        if not self.client or not self.client.name:
            return f"Unnamed Client Master Services Agreement (MSA) {datetime.now().strftime('%Y%m%d')}.docx"

        return f"{self.client.name} Master Services Agreement (MSA) {datetime.now().strftime('%Y%m%d')}.docx"

    def get_baa_template_path(self):
        """
        Get the path to the BAA template for this engagement.

        Returns:
            str: The path to the BAA template
        """
        return "templates/base_templates/baa/Template_Business_Associate_Agreement.docx"

    def get_baa_filename(self):
        """
        Get the filename for the BAA document.

        Returns:
            str: The filename for the BAA document
        """
        from datetime import datetime

        if not self.client or not self.client.name:
            return f"Unnamed Client Business Associate Agreement (BAA) {datetime.now().strftime('%Y%m%d')}.docx"

        return f"{self.client.name} Business Associate Agreement (BAA) {datetime.now().strftime('%Y%m%d')}.docx"

    def get_dpa_template_path(self):
        """
        Get the path to the DPA template for this engagement.

        Returns:
            str: The path to the DPA template
        """
        return "templates/base_templates/dpa/Template_Data_Processing_Agreement.docx"

    def get_dpa_filename(self):
        """
        Get the filename for the DPA document.

        Returns:
            str: The filename for the DPA document
        """
        from datetime import datetime

        if not self.client or not self.client.name:
            return f"Unnamed Client Data Processing Agreement (DPA) {datetime.now().strftime('%Y%m%d')}.docx"

        return f"{self.client.name} Data Processing Agreement (DPA) {datetime.now().strftime('%Y%m%d')}.docx"

    def get_sow_filename(self):
        """
        Get the filename for the SOW document.

        Returns:
            str: The filename for the SOW document
        """
        from datetime import datetime

        if not self.client or not self.client.name:
            return f"Unnamed Client Recovery & Remediation - SOW {datetime.now().strftime('%Y%m%d')}.docx"

        return f"{self.client.name} Recovery & Remediation - SOW {datetime.now().strftime('%Y%m%d')}.docx"

    def get_rr_sow_filename(self):
        """
        Get the filename for the RR SOW document.
        This is kept for backward compatibility.

        Returns:
            str: The filename for the RR SOW document
        """
        return self.get_sow_filename()

    def calculate_pricing(self):
        """
        Calculate pricing for this RR engagement.

        Returns:
            dict: A dictionary containing pricing information
        """
        pricing = {}

        # Get the hourly rate for Recovery and Remediation
        if self.client and self.client.insurance_carrier:
            pricing["rr_hourly_rate"] = get_carrier_rate(self.client.insurance_carrier, "Recovery and Remediation")
            pricing["rr_rate"] = pricing["rr_hourly_rate"]  # Add the rr_rate placeholder for consistency
        else:
            pricing["rr_hourly_rate"] = 275.0  # Default rate
            pricing["rr_rate"] = 275.0  # Default rate

        # Calculate hours based on remote/onsite and resource count
        # Single price model - no ranges
        if self.is_remote:
            # For remote support: flat 100 hours total (not per resource)
            total_hours = 100
        else:
            # For onsite support: 150 hours per resource
            hours = 150

            # Special case for Beazley
            if self.client and self.client.insurance_carrier == "Beazley":
                # For Beazley with onsite support: 100 hours per resource
                hours = 100
                # Store the additional 50 hours that would go to Phase 2 of IR SOW
                pricing["beazley_ir_phase2_additional_hours"] = 50 * self.resource_count

            # Multiply by resource count for onsite
            total_hours = hours * self.resource_count

        # Use single price
        pricing["rr_total_hours"] = total_hours

        # Calculate costs
        total_cost = int(pricing["rr_hourly_rate"] * total_hours)

        # Single price model - no ranges
        pricing["rr_total_cost"] = f"${total_cost:,}"

        # Add a placeholder for backward compatibility
        pricing["rr_total_cost_range"] = pricing["rr_total_cost"]

        return pricing

    def get_placeholders(self):
        """
        Get the placeholders for document generation.

        Returns:
            dict: A dictionary of placeholders and their values
        """
        from datetime import datetime

        # Calculate pricing
        try:
            pricing = self.calculate_pricing()
            if pricing is None:
                print("Warning: Pricing calculation returned None, using default pricing")
                pricing = {}
        except Exception as e:
            print(f"Error calculating pricing: {e}")
            import traceback
            traceback.print_exc()
            pricing = {}

        # Check if RichText is available for formatting
        richtext_available = True
        try:
            from docxtpl import RichText
        except ImportError:
            richtext_available = False
            print("Warning: RichText not available, using plain strings")

        # Use RichText for client and law firm if available, otherwise use plain strings
        if richtext_available:
            from docxtpl import RichText
            client_name = RichText(self.client.name) if self.client and self.client.name else RichText("Client Name")
            law_firm_name = RichText(self.client.law_firm) if self.client and self.client.law_firm else RichText("Law Firm")
        else:
            client_name = self.client.name if self.client and self.client.name else "Client Name"
            law_firm_name = self.client.law_firm if self.client and self.client.law_firm else "Law Firm"

        # Create the placeholders dictionary
        placeholders = {
            # Client information
            "client": client_name,
            "client_name": client_name,
            "client_address": self.client.address if self.client and self.client.address else "Client Address",
            "lawfirm": law_firm_name,
            "law_firm": law_firm_name,

            # Date information
            "formatted_date": datetime.now().strftime("%B %d, %Y"),
            "date": datetime.now().strftime("%B %d, %Y"),

            # RR-specific information
            "assistance_type": "Remote Assistance" if self.is_remote else "Onsite Assistance",
            "resource_count": str(self.resource_count),
            "onsite_resources_count": str(self.onsite_resources_count),

            # Pricing information from calculate_pricing
            "rr_hourly_rate": pricing.get("rr_hourly_rate", 275),
            "rr_rate": pricing.get("rr_rate", 275),
            "rr_total_hours": pricing.get("rr_total_hours", 100),
            "rr_total_cost": pricing.get("rr_total_cost", "$27,500"),
            "rr_total_cost_range": pricing.get("rr_total_cost_range", "$27,500"),

            # Additional Beazley-specific placeholders
            "beazley_ir_phase2_additional_hours": pricing.get("beazley_ir_phase2_additional_hours", 0),

            # Service options
            "include_decryption": "Yes" if self.include_decryption else "No",
        }

        # Add any additional pricing placeholders that might be in the pricing dictionary
        for key, value in pricing.items():
            if key not in placeholders:
                placeholders[key] = value

        return placeholders
