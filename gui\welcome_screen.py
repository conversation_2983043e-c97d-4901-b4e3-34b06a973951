"""
Welcome screen UI for PACE application.
"""

import sys
import os
from PySide6.QtWidgets import (
    Q<PERSON><PERSON>t, QLabel, QLineEdit, QComboBox, QRadioButton, QButtonGroup,
    QPushButton, QVBoxLayout, QHBoxLayout, QFormLayout, QGroupBox,
    QApplication, QCheckBox
)
from PySide6.QtCore import Qt, Signal

from data.carriers import get_carrier_names
from data.lawfirms import get_law_firm_names
from models.client import Client
from gui.address_widget import AddressWidget

class WelcomeScreen(QWidget):
    """
    Welcome screen for the PACE application.
    """

    # Signal emitted when the user clicks the Next button
    next_clicked = Signal(Client, str)

    def __init__(self):
        """
        Initialize the welcome screen.
        """
        super().__init__()

        self.init_ui()

    def init_ui(self):
        """
        Initialize the UI.
        """
        # Set up the main layout
        main_layout = QVBoxLayout()

        # Add spacing at the top
        main_layout.addSpacing(10)

        # Add the developer information
        developer_label = QLabel("Developed by: Chad Poll<PERSON>")
        developer_label.setStyleSheet("font-style: italic;")
        main_layout.addWidget(developer_label)

        # Add a separator
        main_layout.addSpacing(20)

        # Create a form layout for the client information
        form_layout = QFormLayout()

        # Add the client name field
        self.client_name_edit = QLineEdit()
        form_layout.addRow("Enter Client Name:", self.client_name_edit)

        # Add the client address field with autocomplete
        self.client_address_widget = AddressWidget()
        form_layout.addRow("Enter Client Address:", self.client_address_widget)

        # Add the insurance carrier dropdown
        self.carrier_combo = QComboBox()
        self.carrier_combo.addItems(get_carrier_names())
        self.carrier_combo.addItem("Other")
        form_layout.addRow("Insurance Carrier:", self.carrier_combo)

        # Add the other carrier field
        self.other_carrier_edit = QLineEdit()
        self.other_carrier_edit.setVisible(False)
        form_layout.addRow("Other Carrier:", self.other_carrier_edit)

        # Connect the carrier combo box to show/hide the other carrier field
        self.carrier_combo.currentTextChanged.connect(self.on_carrier_changed)

        # Add the law firm dropdown
        self.lawfirm_combo = QComboBox()
        self.lawfirm_combo.addItems(get_law_firm_names())
        self.lawfirm_combo.addItem("Other")
        form_layout.addRow("Law Firm:", self.lawfirm_combo)

        # Add the other law firm field
        self.other_lawfirm_edit = QLineEdit()
        self.other_lawfirm_edit.setVisible(False)
        form_layout.addRow("Other Law Firm:", self.other_lawfirm_edit)

        # Connect the law firm combo box to show/hide the other law firm field
        self.lawfirm_combo.currentTextChanged.connect(self.on_lawfirm_changed)

        # Add the TASB checkbox (for Baker & Hostetler LLP)
        self.tasb_checkbox = QCheckBox("TASB Matter (Texas Association of School Boards)")
        self.tasb_checkbox.setVisible(False)
        form_layout.addRow("", self.tasb_checkbox)

        # Add the form layout to the main layout
        main_layout.addLayout(form_layout)

        # Add a separator
        main_layout.addSpacing(20)

        # Create a group box for the engagement type
        engagement_group = QGroupBox("Type of Primary Engagement:")
        engagement_layout = QVBoxLayout()

        # Create radio buttons for the engagement types
        self.dfir_radio = QRadioButton("DFIR (Digital Forensics Incident Response)")
        self.taci_radio = QRadioButton("TACI (Threat Actor Communications and Intelligence)")
        self.bec_radio = QRadioButton("BEC (Business Email Compromise)")
        self.rr_radio = QRadioButton("Recovery and Remediation")

        # Add the radio buttons to a button group
        self.engagement_group = QButtonGroup()
        self.engagement_group.addButton(self.dfir_radio)
        self.engagement_group.addButton(self.taci_radio)
        self.engagement_group.addButton(self.bec_radio)
        self.engagement_group.addButton(self.rr_radio)

        # Add the radio buttons to the layout
        engagement_layout.addWidget(self.dfir_radio)
        engagement_layout.addWidget(self.taci_radio)
        engagement_layout.addWidget(self.bec_radio)
        engagement_layout.addWidget(self.rr_radio)

        # Set the layout for the group box
        engagement_group.setLayout(engagement_layout)

        # Add the group box to the main layout
        main_layout.addWidget(engagement_group)

        # Add a separator
        main_layout.addSpacing(20)

        # Add the Next button
        self.next_button = QPushButton("Next")
        self.next_button.clicked.connect(self.on_next_clicked)
        main_layout.addWidget(self.next_button, alignment=Qt.AlignRight)

        # Set the main layout
        self.setLayout(main_layout)

        # Set the window properties
        self.setWindowTitle("PACE - Welcome")
        self.resize(600, 400)

    def on_carrier_changed(self, text):
        """
        Handle changes to the carrier combo box.

        Args:
            text (str): The selected carrier
        """
        self.other_carrier_edit.setVisible(text == "Other")

    def on_lawfirm_changed(self, text):
        """
        Handle changes to the law firm combo box.

        Args:
            text (str): The selected law firm
        """
        self.other_lawfirm_edit.setVisible(text == "Other")

        # Show TASB checkbox only for Baker & Hostetler LLP
        self.tasb_checkbox.setVisible(text == "Baker & Hostetler LLP")
        if text != "Baker & Hostetler LLP":
            self.tasb_checkbox.setChecked(False)

    # Address handling is now done by the AddressWidget

    def on_next_clicked(self):
        """
        Handle the Next button click.
        """
        # Get the client information
        client_name = self.client_name_edit.text()
        client_address = self.client_address_widget.text()

        # Get the insurance carrier
        if self.carrier_combo.currentText() == "Other":
            insurance_carrier = self.other_carrier_edit.text()
        else:
            insurance_carrier = self.carrier_combo.currentText()

        # Get the law firm
        if self.lawfirm_combo.currentText() == "Other":
            law_firm = self.other_lawfirm_edit.text()
        else:
            law_firm = self.lawfirm_combo.currentText()

        # Create a Client object
        client = Client(client_name, client_address, insurance_carrier, law_firm)

        # Set TASB property if checkbox is checked
        client.is_tasb = self.tasb_checkbox.isChecked()

        # Get the engagement type
        engagement_type = ""
        if self.dfir_radio.isChecked():
            engagement_type = "DFIR"
        elif self.taci_radio.isChecked():
            engagement_type = "TACI"
        elif self.bec_radio.isChecked():
            engagement_type = "BEC"
        elif self.rr_radio.isChecked():
            engagement_type = "RR"

        # Emit the next_clicked signal
        self.next_clicked.emit(client, engagement_type)


if __name__ == "__main__":
    app = QApplication(sys.argv)
    welcome_screen = WelcomeScreen()
    welcome_screen.show()
    sys.exit(app.exec())
