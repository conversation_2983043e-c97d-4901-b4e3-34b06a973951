"""
Wrapper for DocxTemplate to ensure consistent document generation.
"""

import os
import shutil
from docxtpl import DocxTemplate
import jinja2

def generate_document_with_docxtpl(template_path, output_path, placeholders):
    """
    Generate a document using DocxTemplate.
    
    Args:
        template_path (str): Path to the template file
        output_path (str): Path to save the generated document
        placeholders (dict): Placeholders to replace in the template
        
    Returns:
        str: Path to the generated document, or None if generation failed
    """
    try:
        # Check if the template exists
        if not os.path.exists(template_path):
            print(f"Template not found: {template_path}")
            return None
            
        # Create the output directory if it doesn't exist
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        
        # Load the template
        doc = DocxTemplate(template_path)
        
        # Create a custom Jinja2 environment to handle ampersands correctly
        custom_jinja_env = jinja2.Environment(autoescape=False)
        doc.jinja_env = custom_jinja_env
        
        # Replace placeholders
        doc.render(placeholders)
        
        # Save the document
        doc.save(output_path)
        
        print(f"Generated document from {template_path} to {output_path} using DocxTemplate")
        
        # Verify the document was created
        if os.path.exists(output_path):
            return output_path
        else:
            print(f"Document generation failed: Output file not found at {output_path}")
            return None
    except Exception as e:
        print(f"Error generating document: {str(e)}")
        import traceback
        traceback.print_exc()
        
        # For debugging, create a simple text file with the placeholders
        debug_path = output_path.replace('.docx', '_debug.txt')
        try:
            with open(debug_path, 'w') as f:
                f.write(f"Error generating document: {str(e)}\n\n")
                f.write(f"Template path: {template_path}\n")
                f.write(f"Output path: {output_path}\n\n")
                f.write("Placeholders:\n")
                for key, value in placeholders.items():
                    f.write(f"{key}: {value}\n")
            print(f"Debug info written to: {debug_path}")
        except Exception as debug_error:
            print(f"Error writing debug info: {str(debug_error)}")
        
        # As a last resort, try to copy the template to the output path
        try:
            shutil.copy2(template_path, output_path)
            print(f"Created a copy of the template at {output_path} as a fallback")
            return output_path
        except Exception as copy_error:
            print(f"Error copying template: {str(copy_error)}")
            return None
